{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC File Discovery and Processing\n", "\n", "This notebook discovers and processes IFC (Industry Foundation Classes) files for building information modeling and construction analysis.\n", "\n", "**Stage**: Preprocessing - IFC Pipeline  \n", "**Input Data**: IFC files in data/ directory  \n", "**Output**: Discovered IFC files and basic metadata extraction  \n", "**Format**: File inventory and structured metadata  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Objective\n", "\n", "Discover and process IFC files to:\n", "- Locate all IFC files in the project data structure\n", "- Extract basic file metadata and properties\n", "- Validate file integrity and accessibility\n", "- Prepare data for downstream BIM analysis workflows"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approach\n", "\n", "1. Recursively discover IFC files using cross-platform file operations\n", "2. Validate file accessibility and format\n", "3. Extract basic metadata using ifcopenshell (if available)\n", "4. Generate summary statistics and file inventory\n", "5. Export results for downstream processing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Code"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ifcopenshell library loaded successfully\n", "IFC File Discovery and Processing - Starting...\n", "Timestamp: 2025-07-03 18:52:17\n"]}], "source": ["import logging\n", "import os\n", "from pathlib import Path\n", "from collections import defaultdict\n", "from datetime import datetime\n", "from typing import List, Dict, Optional, Any\n", "\n", "# Try to import IFC processing library\n", "try:\n", "    import ifcopenshell\n", "    IFC_AVAILABLE = True\n", "    print(\"ifcopenshell library loaded successfully\")\n", "except ImportError:\n", "    IFC_AVAILABLE = False\n", "    print(\"Warning: ifcopenshell not available - basic file discovery only\")\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"IFC File Discovery and Processing - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-03 18:52:49,584 - INFO - Data directory verified: ../../../asbuilt-foundation-analysis/data\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Project root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis\n", "Data path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data\n"]}], "source": ["# Define paths for IFC file discovery\n", "project_root = Path('../../../asbuilt-foundation-analysis')  # Adjust to project root from notebooks/preprocessing/ifc/\n", "data_path = project_root / 'data'\n", "\n", "print(f\"Project root: {project_root.resolve()}\")\n", "print(f\"Data path: {data_path.resolve()}\")\n", "\n", "# Verify the data directory exists\n", "if not data_path.exists():\n", "    logger.error(f\"Data directory does not exist: {data_path}\")\n", "    raise FileNotFoundError(f\"Directory not found: {data_path}\")\n", "elif not data_path.is_dir():\n", "    logger.error(f\"Data path is not a directory: {data_path}\")\n", "    raise NotADirectoryError(f\"Path is not a directory: {data_path}\")\n", "else:\n", "    logger.info(f\"Data directory verified: {data_path}\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-03 18:52:53,576 - INFO - Beginning recursive IFC file discovery\n", "2025-07-03 18:52:53,611 - INFO - IFC discovery completed. Found 1 valid IFC files\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Starting IFC file discovery...\n"]}], "source": ["# IFC file discovery\n", "discovered_ifc_files = []\n", "error_files = []\n", "subdirectory_counts = defaultdict(int)\n", "\n", "print(\"Starting IFC file discovery...\")\n", "logger.info(\"Beginning recursive IFC file discovery\")\n", "\n", "# IFC file extensions to search for\n", "ifc_extensions = ['*.ifc', '*.ifcxml', '*.ifczip']\n", "\n", "try:\n", "    for extension in ifc_extensions:\n", "        ifc_files = data_path.rglob(extension)\n", "        \n", "        for ifc_file in ifc_files:\n", "            try:\n", "                # Verify file is readable and has correct extension\n", "                if ifc_file.is_file() and ifc_file.suffix.lower() in ['.ifc', '.ifcxml', '.ifczip']:\n", "                    # Check if file is readable\n", "                    if os.access(ifc_file, os.R_OK):\n", "                        discovered_ifc_files.append(ifc_file)\n", "                        \n", "                        # Count files by subdirectory\n", "                        relative_path = ifc_file.relative_to(data_path)\n", "                        subdirectory = relative_path.parts[0] if len(relative_path.parts) > 1 else 'root'\n", "                        subdirectory_counts[subdirectory] += 1\n", "                        \n", "                        logger.debug(f\"Discovered IFC: {ifc_file}\")\n", "                    else:\n", "                        error_files.append((ifc_file, \"File not readable\"))\n", "                        logger.warning(f\"IFC file not readable: {ifc_file}\")\n", "                else:\n", "                    error_files.append((ifc_file, \"Invalid file type or not a file\"))\n", "                    logger.warning(f\"Invalid IFC file: {ifc_file}\")\n", "                    \n", "            except Exception as e:\n", "                error_files.append((ifc_file, str(e)))\n", "                logger.error(f\"Error processing file {ifc_file}: {e}\")\n", "                \n", "except Exception as e:\n", "    logger.error(f\"Error during IFC discovery: {e}\")\n", "    raise\n", "\n", "logger.info(f\"IFC discovery completed. Found {len(discovered_ifc_files)} valid IFC files\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Extracting IFC metadata...\n", "Processing: GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "  Success: 693352 entities, schema IFC4\n"]}], "source": ["# Basic IFC metadata extraction (if ifcopenshell is available)\n", "ifc_metadata = {}\n", "\n", "if IFC_AVAILABLE and discovered_ifc_files:\n", "    print(\"\\nExtracting IFC metadata...\")\n", "    \n", "    for ifc_file in discovered_ifc_files:\n", "        file_name = ifc_file.name\n", "        print(f\"Processing: {file_name}\")\n", "        \n", "        try:\n", "            # Open IFC file\n", "            ifc_model = ifcopenshell.open(str(ifc_file))\n", "            \n", "            # Extract basic metadata\n", "            metadata = {\n", "                'file_path': str(ifc_file),\n", "                'file_size_bytes': ifc_file.stat().st_size,\n", "                'schema': ifc_model.schema,\n", "                'entity_count': len(ifc_model.by_type('IfcRoot')),\n", "                'building_elements': len(ifc_model.by_type('IfcBuildingElement')),\n", "                'spaces': len(ifc_model.by_type('IfcSpace')),\n", "                'walls': len(ifc_model.by_type('IfcWall')),\n", "                'columns': len(ifc_model.by_type('IfcColumn')),\n", "                'beams': len(ifc_model.by_type('IfcBeam')),\n", "                'slabs': len(ifc_model.by_type('IfcSlab')),\n", "                'extraction_timestamp': datetime.now().isoformat()\n", "            }\n", "            \n", "            # Try to get project information\n", "            projects = ifc_model.by_type('IfcProject')\n", "            if projects:\n", "                project = projects[0]\n", "                metadata['project_name'] = getattr(project, 'Name', '')\n", "                metadata['project_description'] = getattr(project, 'Description', '')\n", "            \n", "            ifc_metadata[file_name] = metadata\n", "            print(f\"  Success: {metadata['entity_count']} entities, schema {metadata['schema']}\")\n", "            \n", "        except Exception as e:\n", "            error_msg = str(e)\n", "            logger.error(f\"Error processing IFC file {file_name}: {error_msg}\")\n", "            print(f\"  Error: {error_msg}\")\n", "            \n", "            # Store basic file info even if IFC parsing fails\n", "            ifc_metadata[file_name] = {\n", "                'file_path': str(ifc_file),\n", "                'file_size_bytes': ifc_file.stat().st_size,\n", "                'error': error_msg,\n", "                'extraction_timestamp': datetime.now().isoformat()\n", "            }\n", "else:\n", "    print(\"\\nIFC metadata extraction skipped (ifcopenshell not available or no IFC files)\")\n", "    \n", "    # Create basic metadata for discovered files\n", "    for ifc_file in discovered_ifc_files:\n", "        file_name = ifc_file.name\n", "        ifc_metadata[file_name] = {\n", "            'file_path': str(ifc_file),\n", "            'file_size_bytes': ifc_file.stat().st_size,\n", "            'extraction_timestamp': datetime.now().isoformat()\n", "        }"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "IFC FILE DISCOVERY SUMMARY\n", "============================================================\n", "\n", "Total IFC files discovered: 1\n", "Files with errors: 0\n", "Files with metadata extracted: 1\n", "\n", "Files by subdirectory:\n", "  raw: 1 files\n", "\n", "============================================================\n"]}], "source": ["# Display IFC discovery summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"IFC FILE DISCOVERY SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\nTotal IFC files discovered: {len(discovered_ifc_files)}\")\n", "print(f\"Files with errors: {len(error_files)}\")\n", "print(f\"Files with metadata extracted: {len(ifc_metadata)}\")\n", "\n", "if subdirectory_counts:\n", "    print(\"\\nFiles by subdirectory:\")\n", "    for subdirectory, count in sorted(subdirectory_counts.items()):\n", "        print(f\"  {subdirectory}: {count} files\")\n", "\n", "if error_files:\n", "    print(\"\\nFiles with errors:\")\n", "    for error_file, error_msg in error_files:\n", "        print(f\"  {error_file}: {error_msg}\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "DISCOVERED IFC FILES:\n", "----------------------------------------\n", "  1. raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc (187.03 MB)\n", "     Schema: IFC4, Entities: 693352\n", "     Project: Project Number\n"]}], "source": ["# Display detailed IFC file listing\n", "if discovered_ifc_files:\n", "    print(\"\\nDISCOVERED IFC FILES:\")\n", "    print(\"-\" * 40)\n", "    \n", "    for i, ifc_file in enumerate(discovered_ifc_files, 1):\n", "        # Show relative path from data directory for cleaner output\n", "        relative_path = ifc_file.relative_to(data_path)\n", "        file_size = ifc_file.stat().st_size\n", "        file_size_mb = file_size / (1024 * 1024)  # Convert to MB\n", "        \n", "        print(f\"{i:3d}. {relative_path} ({file_size_mb:.2f} MB)\")\n", "        \n", "        # Show metadata if available\n", "        file_name = ifc_file.name\n", "        if file_name in ifc_metadata:\n", "            metadata = ifc_metadata[file_name]\n", "            if 'schema' in metadata:\n", "                print(f\"     Schema: {metadata['schema']}, Entities: {metadata.get('entity_count', 'N/A')}\")\n", "            if 'project_name' in metadata and metadata['project_name']:\n", "                print(f\"     Project: {metadata['project_name']}\")\n", "        \n", "        # Log full path for debugging if needed\n", "        logger.debug(f\"Full path {i}: {ifc_file}\")\n", "else:\n", "    print(\"\\nNo IFC files were discovered in the data directory.\")\n", "    logger.warning(\"No IFC files found - this may indicate an issue with the data directory structure\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "EXPORTING IFC RESULTS...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-03 18:53:25,165 - INFO - IFC discovery results exported successfully\n"]}, {"name": "stdout", "output_type": "stream", "text": ["IFC metadata saved to: ../../../asbuilt-foundation-analysis/data/ifc_metadata.csv\n", "\n", "Results exported to variables:\n", "  - discovered_ifc_files: 1 file paths\n", "  - ifc_metadata_results: 1 metadata records\n", "  - ifc_discovery_summary: Summary statistics\n"]}], "source": ["# Export results for downstream processing\n", "print(\"\\nEXPORTING IFC RESULTS...\")\n", "\n", "# Create a list of full file paths as strings for easy serialization\n", "ifc_file_paths = [str(ifc_file) for ifc_file in discovered_ifc_files]\n", "\n", "# Store results in variables for use by subsequent notebooks\n", "globals()['discovered_ifc_files'] = ifc_file_paths\n", "globals()['ifc_metadata_results'] = ifc_metadata\n", "globals()['ifc_discovery_summary'] = {\n", "    'total_files': len(discovered_ifc_files),\n", "    'error_files': len(error_files),\n", "    'subdirectory_counts': dict(subdirectory_counts),\n", "    'discovery_timestamp': datetime.now().isoformat(),\n", "    'ifcopenshell_available': IFC_AVAILABLE\n", "}\n", "\n", "# Save to CSV if we have metadata\n", "if ifc_metadata:\n", "    import pandas as pd\n", "    \n", "    # Create DataFrame from metadata\n", "    metadata_records = []\n", "    for file_name, metadata in ifc_metadata.items():\n", "        record = {'file_name': file_name}\n", "        record.update(metadata)\n", "        metadata_records.append(record)\n", "    \n", "    ifc_df = pd.DataFrame(metadata_records)\n", "    \n", "    # Save to CSV\n", "    output_path = project_root / 'data' / 'ifc_metadata.csv'\n", "    try:\n", "        ifc_df.to_csv(output_path, index=False)\n", "        print(f\"IFC metadata saved to: {output_path}\")\n", "    except Exception as e:\n", "        logger.warning(f\"Could not save IFC metadata CSV: {e}\")\n", "\n", "print(f\"\\nResults exported to variables:\")\n", "print(f\"  - discovered_ifc_files: {len(ifc_file_paths)} file paths\")\n", "print(f\"  - ifc_metadata_results: {len(ifc_metadata)} metadata records\")\n", "print(f\"  - ifc_discovery_summary: Summary statistics\")\n", "\n", "logger.info(\"IFC discovery results exported successfully\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-03 18:53:37,553 - INFO - IFC discovery and processing notebook execution completed\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "IFC DISCOVERY AND PROCESSING COMPLETED\n", "============================================================\n", "Successfully discovered 1 IFC files\n", "Extracted metadata from 1 files\n", "Total IFC entities found: 693352\n", "Ready for downstream BIM analysis workflows\n", "\n", "Completion time: 2025-07-03 18:53:37\n"]}], "source": ["# Final status report\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"IFC DISCOVERY AND PROCESSING COMPLETED\")\n", "print(\"=\"*60)\n", "\n", "if discovered_ifc_files:\n", "    print(f\"Successfully discovered {len(discovered_ifc_files)} IFC files\")\n", "    if IFC_AVAILABLE:\n", "        print(f\"Extracted metadata from {len(ifc_metadata)} files\")\n", "        total_entities = sum(metadata.get('entity_count', 0) for metadata in ifc_metadata.values())\n", "        print(f\"Total IFC entities found: {total_entities}\")\n", "    print(\"Ready for downstream BIM analysis workflows\")\n", "else:\n", "    print(\"No IFC files discovered - please verify data directory structure\")\n", "\n", "if not IFC_AVAILABLE:\n", "    print(\"\\nNote: Install ifcopenshell for advanced IFC processing: pip install ifcopenshell\")\n", "\n", "print(f\"\\nCompletion time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "logger.info(\"IFC discovery and processing notebook execution completed\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}
{"cells": [{"cell_type": "code", "execution_count": null, "id": "ecce17bc", "metadata": {}, "outputs": [], "source": ["import json\n", "import numpy as np\n", "from pathlib import Path\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n"]}, {"cell_type": "code", "execution_count": null, "id": "6cbffbef", "metadata": {}, "outputs": [], "source": ["results_dir = Path(\"../../../data/output_runs/icp_alignment\")\n", "methods = [\"csf\", \"pmf\", \"ransac\", \"ransac_pmf\"]\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "id": "0a2ba47b", "metadata": {}, "outputs": [], "source": ["all_results = []\n", "\n", "for method in methods:\n", "    metrics_path = results_dir / method / f\"icp_metrics_{method}.json\"\n", "    transform_path = results_dir / method / f\"icp_transformation_{method}.npy\"\n", "\n", "    if metrics_path.exists() and transform_path.exists():\n", "        with open(metrics_path) as f:\n", "            data = json.load(f)\n", "\n", "        transform = np.load(transform_path)\n", "\n", "        # Flatten and map correct keys\n", "        row = {\n", "            \"method\": method,\n", "            \"rmse\": data[\"error_metrics\"][\"rmse_meters\"],\n", "            \"max_deviation\": data[\"error_metrics\"][\"max_deviation_meters\"],\n", "            \"mean_deviation\": data[\"error_metrics\"][\"mean_deviation_meters\"],\n", "            \"median_deviation\": data[\"error_metrics\"][\"median_deviation_meters\"],\n", "            \"std_deviation\": data[\"error_metrics\"][\"std_deviation_meters\"],\n", "            \"fitness\": data[\"icp_results\"][\"fitness\"],\n", "            \"inlier_rmse\": data[\"icp_results\"][\"inlier_rmse\"],\n", "            \"transformation\": transform.tolist()\n", "        }\n", "\n", "        all_results.append(row)\n", "    else:\n", "        print(f\"Missing data for method: {method}\")\n", "\n", "df = pd.DataFrame(all_results)\n", "\n", "df = df[[\n", "    \"method\", \"rmse\", \"max_deviation\", \"mean_deviation\",\n", "    \"median_deviation\", \"std_deviation\", \"fitness\", \"inlier_rmse\"\n", "]]"]}, {"cell_type": "code", "execution_count": 13, "id": "1b2315e9", "metadata": {}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_daa51_row0_col1, #T_daa51_row3_col6 {\n", "  background-color: #006837;\n", "  color: #f1f1f1;\n", "}\n", "#T_daa51_row0_col6, #T_daa51_row3_col1 {\n", "  background-color: #a50026;\n", "  color: #f1f1f1;\n", "}\n", "#T_daa51_row1_col1, #T_daa51_row2_col1 {\n", "  background-color: #b3df72;\n", "  color: #000000;\n", "}\n", "#T_daa51_row1_col6 {\n", "  background-color: #fff1a8;\n", "  color: #000000;\n", "}\n", "#T_daa51_row2_col6 {\n", "  background-color: #ecf7a6;\n", "  color: #000000;\n", "}\n", "</style>\n", "<table id=\"T_daa51\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_daa51_level0_col0\" class=\"col_heading level0 col0\" >method</th>\n", "      <th id=\"T_daa51_level0_col1\" class=\"col_heading level0 col1\" >rmse</th>\n", "      <th id=\"T_daa51_level0_col2\" class=\"col_heading level0 col2\" >max_deviation</th>\n", "      <th id=\"T_daa51_level0_col3\" class=\"col_heading level0 col3\" >mean_deviation</th>\n", "      <th id=\"T_daa51_level0_col4\" class=\"col_heading level0 col4\" >median_deviation</th>\n", "      <th id=\"T_daa51_level0_col5\" class=\"col_heading level0 col5\" >std_deviation</th>\n", "      <th id=\"T_daa51_level0_col6\" class=\"col_heading level0 col6\" >fitness</th>\n", "      <th id=\"T_daa51_level0_col7\" class=\"col_heading level0 col7\" >inlier_rmse</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_daa51_level0_row0\" class=\"row_heading level0 row0\" >1</th>\n", "      <td id=\"T_daa51_row0_col0\" class=\"data row0 col0\" >pmf</td>\n", "      <td id=\"T_daa51_row0_col1\" class=\"data row0 col1\" >6.638558</td>\n", "      <td id=\"T_daa51_row0_col2\" class=\"data row0 col2\" >40.758334</td>\n", "      <td id=\"T_daa51_row0_col3\" class=\"data row0 col3\" >5.858230</td>\n", "      <td id=\"T_daa51_row0_col4\" class=\"data row0 col4\" >5.367501</td>\n", "      <td id=\"T_daa51_row0_col5\" class=\"data row0 col5\" >3.122755</td>\n", "      <td id=\"T_daa51_row0_col6\" class=\"data row0 col6\" >0.001939</td>\n", "      <td id=\"T_daa51_row0_col7\" class=\"data row0 col7\" >0.374837</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_daa51_level0_row1\" class=\"row_heading level0 row1\" >2</th>\n", "      <td id=\"T_daa51_row1_col0\" class=\"data row1 col0\" >ransac</td>\n", "      <td id=\"T_daa51_row1_col1\" class=\"data row1 col1\" >10.797042</td>\n", "      <td id=\"T_daa51_row1_col2\" class=\"data row1 col2\" >66.075987</td>\n", "      <td id=\"T_daa51_row1_col3\" class=\"data row1 col3\" >8.955344</td>\n", "      <td id=\"T_daa51_row1_col4\" class=\"data row1 col4\" >7.756686</td>\n", "      <td id=\"T_daa51_row1_col5\" class=\"data row1 col5\" >6.031412</td>\n", "      <td id=\"T_daa51_row1_col6\" class=\"data row1 col6\" >0.001345</td>\n", "      <td id=\"T_daa51_row1_col7\" class=\"data row1 col7\" >0.372878</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_daa51_level0_row2\" class=\"row_heading level0 row2\" >3</th>\n", "      <td id=\"T_daa51_row2_col0\" class=\"data row2 col0\" >ransac_pmf</td>\n", "      <td id=\"T_daa51_row2_col1\" class=\"data row2 col1\" >10.802542</td>\n", "      <td id=\"T_daa51_row2_col2\" class=\"data row2 col2\" >66.062776</td>\n", "      <td id=\"T_daa51_row2_col3\" class=\"data row2 col3\" >8.965235</td>\n", "      <td id=\"T_daa51_row2_col4\" class=\"data row2 col4\" >7.769602</td>\n", "      <td id=\"T_daa51_row2_col5\" class=\"data row2 col5\" >6.026565</td>\n", "      <td id=\"T_daa51_row2_col6\" class=\"data row2 col6\" >0.001223</td>\n", "      <td id=\"T_daa51_row2_col7\" class=\"data row2 col7\" >0.369786</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_daa51_level0_row3\" class=\"row_heading level0 row3\" >0</th>\n", "      <td id=\"T_daa51_row3_col0\" class=\"data row3 col0\" >csf</td>\n", "      <td id=\"T_daa51_row3_col1\" class=\"data row3 col1\" >19.374331</td>\n", "      <td id=\"T_daa51_row3_col2\" class=\"data row3 col2\" >97.717881</td>\n", "      <td id=\"T_daa51_row3_col3\" class=\"data row3 col3\" >14.496025</td>\n", "      <td id=\"T_daa51_row3_col4\" class=\"data row3 col4\" >10.859151</td>\n", "      <td id=\"T_daa51_row3_col5\" class=\"data row3 col5\" >12.854181</td>\n", "      <td id=\"T_daa51_row3_col6\" class=\"data row3 col6\" >0.000638</td>\n", "      <td id=\"T_daa51_row3_col7\" class=\"data row3 col7\" >0.365040</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x156916550>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display summary\n", "df.sort_values(\"rmse\").style.background_gradient(subset=[\"rmse\", \"fitness\"], cmap=\"RdYlGn_r\")\n"]}, {"cell_type": "code", "execution_count": 16, "id": "ce6df88e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Method: CSF\n", "  RMSE: 19.37 m — High Error\n", "  ICP Fitness: 0.000638 — Poor match\n", "  Max deviation: 97.72 m\n", "  Inlier RMSE: 0.365 m\n", "\n", "Method: PMF\n", "  RMSE: 6.64 m — High Error\n", "  ICP Fitness: 0.001939 — Poor match\n", "  Max deviation: 40.76 m\n", "  Inlier RMSE: 0.375 m\n", "\n", "Method: RANSAC\n", "  RMSE: 10.80 m — High Error\n", "  ICP Fitness: 0.001345 — Poor match\n", "  Max deviation: 66.08 m\n", "  Inlier RMSE: 0.373 m\n", "\n", "Method: RANSAC_PMF\n", "  RMSE: 10.80 m — High Error\n", "  ICP Fitness: 0.001223 — Poor match\n", "  Max deviation: 66.06 m\n", "  Inlier RMSE: 0.370 m\n"]}], "source": ["# Explanation of alignment performance\n", "for idx, row in df.iterrows():\n", "    print(f\"\\nMethod: {row['method'].upper()}\")\n", "    print(f\"  RMSE: {row['rmse']:.2f} m — {'Acceptable' if row['rmse'] < 1 else 'High Error'}\")\n", "    print(f\"  ICP Fitness: {row['fitness']:.6f} — {'Good' if row['fitness'] > 0.5 else 'Poor match'}\")\n", "    print(f\"  Max deviation: {row['max_deviation']:.2f} m\")\n", "    print(f\"  Inlier RMSE: {row['inlier_rmse']:.3f} m\")\n"]}, {"cell_type": "code", "execution_count": 17, "id": "97722b4f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Compare RMSE and Fitness\n", "fig, axes = plt.subplots(1, 2, figsize=(12, 4))\n", "df.plot(x=\"method\", y=\"rmse\", kind=\"bar\", ax=axes[0], title=\"RMSE per Method\", legend=False)\n", "df.plot(x=\"method\", y=\"fitness\", kind=\"bar\", ax=axes[1], title=\"ICP Fitness per Method\", legend=False)\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 18, "id": "b60cf551", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformation matrix for 'pmf':\n", "\n", "[[ 1.000000e+00  0.000000e+00 -0.000000e+00 -5.778100e+02]\n", " [-0.000000e+00  1.000000e+00  1.000000e-03  5.136500e+01]\n", " [ 0.000000e+00 -1.000000e-03  1.000000e+00  3.250247e+03]\n", " [ 0.000000e+00  0.000000e+00  0.000000e+00  1.000000e+00]]\n"]}], "source": ["# View a specific method's transformation matrix\n", "method_to_inspect = \"pmf\"\n", "transform = np.load(results_dir / method_to_inspect / f\"icp_transformation_{method_to_inspect}.npy\")\n", "print(f\"Transformation matrix for '{method_to_inspect}':\\n\")\n", "print(np.round(transform, 3))\n"]}, {"cell_type": "code", "execution_count": null, "id": "cf9eb783", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cf14c47b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}
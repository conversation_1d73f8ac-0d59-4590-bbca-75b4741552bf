{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Papermill Alignment Testing Execution\n", "\n", "This notebook contains individual Papermill commands for systematically testing all ground segmentation methods against alignment algorithms. Each test is in a separate cell for easy individual execution or batch processing.\n", "\n", "**Testing Matrix**: 4 ground methods × 3 alignment notebooks = 12 tests  \n", "**Ground Methods**: CSF, PMF, RANSAC, RANSAC+PMF  \n", "**Alignment Methods**: ICP, Neural Network, Hybrid  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install papermill if needed\n", "#! pip install papermill "]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output directories created at: ../../../data/output_runs/alignment_testing\n", "Testing will begin at: 2025-07-06 13:05:09\n", "\n", "Directory structure:\n", "  ../../../data/output_runs/alignment_testing/csf\n", "  ../../../data/output_runs/alignment_testing/pmf\n", "  ../../../data/output_runs/alignment_testing/ransac\n", "  ../../../data/output_runs/alignment_testing/ransac_pmf\n"]}], "source": ["# Setup environment and create output directories\n", "import os\n", "from pathlib import Path\n", "import subprocess\n", "from datetime import datetime\n", "\n", "# Create output directory structure\n", "output_base = Path(\"../../../data/output_runs/alignment_testing\")\n", "ground_methods = [\"csf\", \"pmf\", \"ransac\", \"ransac_pmf\"]\n", "\n", "for method in ground_methods:\n", "    method_dir = output_base / method\n", "    method_dir.mkdir(parents=True, exist_ok=True)\n", "    (method_dir / \"results\").mkdir(exist_ok=True)\n", "\n", "print(f\"Output directories created at: {output_base}\")\n", "print(f\"Testing will begin at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"\\nDirectory structure:\")\n", "for method in ground_methods:\n", "    print(f\"  {output_base / method}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ## 1. ICP Alignment with Custom Parameters\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running ICP tests...\n", "Input Notebook:  01_icp_alignment.ipynb\n", "Output Notebook: ../../../data/output_runs/alignment_testing/csf/01_icp_alignment_csf.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "No handler found for comm target 'dash'\n", "ICP ALIGNMENT - CSF\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment/csf\n", "Timestamp: 2025-07-06 13:15:52\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Loading point clouds...\n", "Drone scan (non-ground): ../../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "Drone exists: True\n", "IFC point cloud: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "IFC exists: True\n", "\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Loaded drone scan: 13,848 points\n", "Loaded IFC model: 1,359,240 points\n", "\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "\n", "INITIAL POINT CLOUD STATISTICS\n", "\n", "Drone scan (ground truth):\n", "  Points: 13,848\n", "  X range: [435223.72, 436794.15]\n", "  Y range: [5010816.92, 5012539.06]\n", "  Z range: [1.17, 13.14]\n", "\n", "IFC model (to be aligned):\n", "  Points: 1,359,240\n", "  X range: [435267.17, 436719.98]\n", "  Y range: [5010900.69, 5012462.43]\n", "  Z range: [152.87, 161.66]\n", "\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "\n", "COORDINATE SYSTEM ANALYSIS\n", "Drone center: [436024.70, 5011683.28, 2.46]\n", "IFC center:   [435986.35, 5011746.88, 157.34]\n", "Offset:       [38.35, -63.60, -154.87]\n", "\n", "Offset magnitude: 171.76 meters\n", "\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "\n", "Z-SHIFT CORRECTION\n", "Drone Z median: 2.56 m\n", "IFC Z median:   157.34 m\n", "Z-shift needed: -154.78 m\n", "Applied Z-shift of -154.78 m to IFC point cloud\n", "\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "\n", "Z-SHIFT VERIFICATION\n", "IFC Z median after shift: 2.56 m\n", "Remaining Z difference: 0.00 m\n", "Z-shift successful: True\n", "\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "\n", "ICP ALIGNMENT SETUP\n", "Source (IFC): 1,359,240 points\n", "Target (Drone): 13,848 points\n", "Max iterations: 30\n", "Tolerance: 1e-06\n", "\n", "Ending Cell 14-----------------------------------------\n", "Executing Cell 15--------------------------------------\n", "\n", "COARSE ALIGNMENT\n", "Source center: [435986.35, 5011746.88, 2.56]\n", "Target center: [436024.70, 5011683.28, 2.46]\n", "Coarse translation: [38.35, -63.60, -0.09]\n", "Applied coarse alignment\n", "\n", "Ending Cell 15-----------------------------------------\n", "Executing Cell 16--------------------------------------\n", "\n", "FINE ALIGNMENT (ICP)\n", "Running ICP registration...\n", "\n", "ICP completed in 2.08 seconds\n", "Fitness: 0.000638\n", "Inlier RMSE: 0.365040 meters\n", "\n", "Ending Cell 16-----------------------------------------\n", "Executing Cell 17--------------------------------------\n", "\n", "FINAL TRANSFORMATION\n", "Transformation matrix:\n", "[[ 9.99999982e-01 -4.38770884e-05 -1.83744329e-04  2.19692294e+02]\n", " [ 4.39418995e-05  9.99999937e-01  3.52735512e-04 -1.85679487e+01]\n", " [ 1.83728841e-04 -3.52743579e-04  9.99999921e-01  1.68784648e+03]\n", " [ 0.00000000e+00  0.00000000e+00  0.00000000e+00  1.00000000e+00]]\n", "\n", "Translation: [219.692, -18.568, 1687.846] meters\n", "Rotation (approx): X=-0.02°, Y=-0.01°, Z=0.00°\n", "\n", "Ending Cell 17-----------------------------------------\n", "Executing Cell 18--------------------------------------\n", "Ending Cell 18-----------------------------------------\n", "Executing Cell 19--------------------------------------\n", "Saved visualization to: ../../../data/output_runs/icp_alignment/csf/icp_alignment_visualization_csf.png\n", "\n", "<Figure size 1500x1200 with 4 Axes>\n", "Ending Cell 19-----------------------------------------\n", "Executing Cell 20--------------------------------------\n", "Ending Cell 20-----------------------------------------\n", "Executing Cell 21--------------------------------------\n", "\n", "ALIGNMENT ERROR ANALYSIS\n", "Finding nearest neighbors...\n", "\n", "\n", "ERROR METRICS\n", "RMSE: 19.3743 meters\n", "Max deviation: 97.7179 meters\n", "Mean deviation: 14.4960 meters\n", "Std deviation: 12.8542 meters\n", "Median deviation: 10.8592 meters\n", "95th percentile: 40.9319 meters\n", "99th percentile: 68.7781 meters\n", "\n", "Ending Cell 21-----------------------------------------\n", "Executing Cell 22--------------------------------------\n", "Saved error analysis to: ../../../data/output_runs/icp_alignment/csf/icp_error_analysis_csf.png\n", "\n", "<Figure size 1500x500 with 2 Axes>\n", "Ending Cell 22-----------------------------------------\n", "Executing Cell 23--------------------------------------\n", "\n", "SAVING RESULTS\n", "Saved aligned point cloud: ../../../data/output_runs/icp_alignment/csf/icp_aligned_csf.ply\n", "Saved transformation matrix: ../../../data/output_runs/icp_alignment/csf/icp_transformation_csf.npy\n", "Saved metrics: ../../../data/output_runs/icp_alignment/csf/icp_metrics_csf.json\n", "\n", "All results saved to: ../../../data/output_runs/icp_alignment/csf\n", "\n", "Ending Cell 23-----------------------------------------\n", "Executing Cell 24--------------------------------------\n", "\n", "============================================================\n", "ICP ALIGNMENT SUMMARY\n", "============================================================\n", "Site: trino_enel\n", "Ground method: csf\n", "Processing time: 2.08 seconds\n", "\n", "Alignment Quality:\n", "  RMSE: 19.3743 meters\n", "  Max deviation: 97.7179 meters\n", "  ICP fitness: 0.000638\n", "  ICP inlier RMSE: 0.365040 meters\n", "\n", "Alignment Success: POOR\n", "============================================================\n", "\n", "Ending Cell 24-----------------------------------------\n", "Completed ICP + CSF with params: {'voxel_size': 0.05, 'max_iterations': 30, 'tolerance': 1e-06}\n", "Input Notebook:  01_icp_alignment.ipynb\n", "Output Notebook: ../../../data/output_runs/alignment_testing/pmf/01_icp_alignment_pmf.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "No handler found for comm target 'dash'\n", "ICP ALIGNMENT - PMF\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment/pmf\n", "Timestamp: 2025-07-06 13:16:18\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Loading point clouds...\n", "Drone scan (non-ground): ../../../data/processed/trino_enel/ground_segmentation/pmf/trino_enel_nonground.ply\n", "Drone exists: True\n", "IFC point cloud: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "IFC exists: True\n", "\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Loaded drone scan: 36,709 points\n", "Loaded IFC model: 1,359,240 points\n", "\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "\n", "INITIAL POINT CLOUD STATISTICS\n", "\n", "Drone scan (ground truth):\n", "  Points: 36,709\n", "  X range: [435221.67, 436794.15]\n", "  Y range: [5010816.92, 5012539.06]\n", "  Z range: [-0.03, 13.14]\n", "\n", "IFC model (to be aligned):\n", "  Points: 1,359,240\n", "  X range: [435267.17, 436719.98]\n", "  Y range: [5010900.69, 5012462.43]\n", "  Z range: [152.87, 161.66]\n", "\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "\n", "COORDINATE SYSTEM ANALYSIS\n", "Drone center: [436042.27, 5011712.94, 1.26]\n", "IFC center:   [435986.35, 5011746.88, 157.34]\n", "Offset:       [55.93, -33.94, -156.07]\n", "\n", "Offset magnitude: 169.23 meters\n", "\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "\n", "Z-SHIFT CORRECTION\n", "Drone Z median: 0.77 m\n", "IFC Z median:   157.34 m\n", "Z-shift needed: -156.56 m\n", "Applied Z-shift of -156.56 m to IFC point cloud\n", "\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "\n", "Z-SHIFT VERIFICATION\n", "IFC Z median after shift: 0.77 m\n", "Remaining Z difference: 0.00 m\n", "Z-shift successful: True\n", "\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "\n", "ICP ALIGNMENT SETUP\n", "Source (IFC): 1,359,240 points\n", "Target (Drone): 36,709 points\n", "Max iterations: 50\n", "Tolerance: 1e-05\n", "\n", "Ending Cell 14-----------------------------------------\n", "Executing Cell 15--------------------------------------\n", "\n", "COARSE ALIGNMENT\n", "Source center: [435986.35, 5011746.88, 0.77]\n", "Target center: [436042.27, 5011712.94, 1.26]\n", "Coarse translation: [55.93, -33.94, 0.49]\n", "Applied coarse alignment\n", "\n", "Ending Cell 15-----------------------------------------\n", "Executing Cell 16--------------------------------------\n", "\n", "FINE ALIGNMENT (ICP)\n", "Running ICP registration...\n", "\n", "ICP completed in 1.87 seconds\n", "Fitness: 0.001939\n", "Inlier RMSE: 0.374837 meters\n", "\n", "Ending Cell 16-----------------------------------------\n", "Executing Cell 17--------------------------------------\n", "\n", "FINAL TRANSFORMATION\n", "Transformation matrix:\n", "[[ 9.99999983e-01  1.15264660e-04 -1.42984125e-04 -5.77809811e+02]\n", " [-1.15170127e-04  9.99999775e-01  6.60978535e-04  5.13654761e+01]\n", " [ 1.43060280e-04 -6.60962056e-04  9.99999771e-01  3.25024703e+03]\n", " [ 0.00000000e+00  0.00000000e+00  0.00000000e+00  1.00000000e+00]]\n", "\n", "Translation: [-577.810, 51.365, 3250.247] meters\n", "Rotation (approx): X=-0.04°, Y=-0.01°, Z=-0.01°\n", "\n", "Ending Cell 17-----------------------------------------\n", "Executing Cell 18--------------------------------------\n", "Ending Cell 18-----------------------------------------\n", "Executing Cell 19--------------------------------------\n", "Saved visualization to: ../../../data/output_runs/icp_alignment/pmf/icp_alignment_visualization_pmf.png\n", "\n", "<Figure size 1500x1200 with 4 Axes>\n", "Ending Cell 19-----------------------------------------\n", "Executing Cell 20--------------------------------------\n", "Ending Cell 20-----------------------------------------\n", "Executing Cell 21--------------------------------------\n", "\n", "ALIGNMENT ERROR ANALYSIS\n", "Finding nearest neighbors...\n", "\n", "\n", "ERROR METRICS\n", "RMSE: 6.6386 meters\n", "Max deviation: 40.7583 meters\n", "Mean deviation: 5.8582 meters\n", "Std deviation: 3.1228 meters\n", "Median deviation: 5.3675 meters\n", "95th percentile: 11.2899 meters\n", "99th percentile: 14.6391 meters\n", "\n", "Ending Cell 21-----------------------------------------\n", "Executing Cell 22--------------------------------------\n", "Saved error analysis to: ../../../data/output_runs/icp_alignment/pmf/icp_error_analysis_pmf.png\n", "\n", "<Figure size 1500x500 with 2 Axes>\n", "Ending Cell 22-----------------------------------------\n", "Executing Cell 23--------------------------------------\n", "\n", "SAVING RESULTS\n", "Saved aligned point cloud: ../../../data/output_runs/icp_alignment/pmf/icp_aligned_pmf.ply\n", "Saved transformation matrix: ../../../data/output_runs/icp_alignment/pmf/icp_transformation_pmf.npy\n", "Saved metrics: ../../../data/output_runs/icp_alignment/pmf/icp_metrics_pmf.json\n", "\n", "All results saved to: ../../../data/output_runs/icp_alignment/pmf\n", "\n", "Ending Cell 23-----------------------------------------\n", "Executing Cell 24--------------------------------------\n", "\n", "============================================================\n", "ICP ALIGNMENT SUMMARY\n", "============================================================\n", "Site: trino_enel\n", "Ground method: pmf\n", "Processing time: 1.87 seconds\n", "\n", "Alignment Quality:\n", "  RMSE: 6.6386 meters\n", "  Max deviation: 40.7583 meters\n", "  ICP fitness: 0.001939\n", "  ICP inlier RMSE: 0.374837 meters\n", "\n", "Alignment Success: POOR\n", "============================================================\n", "\n", "Ending Cell 24-----------------------------------------\n", "Completed ICP + PMF with params: {'voxel_size': 0.1, 'max_iterations': 50, 'tolerance': 1e-05}\n", "Input Notebook:  01_icp_alignment.ipynb\n", "Output Notebook: ../../../data/output_runs/alignment_testing/ransac/01_icp_alignment_ransac.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "No handler found for comm target 'dash'\n", "ICP ALIGNMENT - RANSAC\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment/ransac\n", "Timestamp: 2025-07-06 13:16:46\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Loading point clouds...\n", "Drone scan (non-ground): ../../../data/processed/trino_enel/ground_segmentation/ransac/trino_enel_nonground.ply\n", "Drone exists: True\n", "IFC point cloud: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "IFC exists: True\n", "\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Loaded drone scan: 22,724 points\n", "Loaded IFC model: 1,359,240 points\n", "\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "\n", "INITIAL POINT CLOUD STATISTICS\n", "\n", "Drone scan (ground truth):\n", "  Points: 22,724\n", "  X range: [435221.67, 436794.15]\n", "  Y range: [5010816.92, 5012539.06]\n", "  Z range: [-0.10, 13.14]\n", "\n", "IFC model (to be aligned):\n", "  Points: 1,359,240\n", "  X range: [435267.17, 436719.98]\n", "  Y range: [5010900.69, 5012462.43]\n", "  Z range: [152.87, 161.66]\n", "\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "\n", "COORDINATE SYSTEM ANALYSIS\n", "Drone center: [436046.70, 5011706.45, 1.79]\n", "IFC center:   [435986.35, 5011746.88, 157.34]\n", "Offset:       [60.35, -40.43, -155.55]\n", "\n", "Offset magnitude: 171.68 meters\n", "\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "\n", "Z-SHIFT CORRECTION\n", "Drone Z median: 1.46 m\n", "IFC Z median:   157.34 m\n", "Z-shift needed: -155.88 m\n", "Applied Z-shift of -155.88 m to IFC point cloud\n", "\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "\n", "Z-SHIFT VERIFICATION\n", "IFC Z median after shift: 1.45 m\n", "Remaining Z difference: 0.00 m\n", "Z-shift successful: True\n", "\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "\n", "ICP ALIGNMENT SETUP\n", "Source (IFC): 1,359,240 points\n", "Target (Drone): 22,724 points\n", "Max iterations: 100\n", "Tolerance: 1e-07\n", "\n", "Ending Cell 14-----------------------------------------\n", "Executing Cell 15--------------------------------------\n", "\n", "COARSE ALIGNMENT\n", "Source center: [435986.35, 5011746.88, 1.45]\n", "Target center: [436046.70, 5011706.45, 1.79]\n", "Coarse translation: [60.35, -40.43, 0.33]\n", "Applied coarse alignment\n", "\n", "Ending Cell 15-----------------------------------------\n", "Executing Cell 16--------------------------------------\n", "\n", "FINE ALIGNMENT (ICP)\n", "Running ICP registration...\n", "\n", "ICP completed in 6.83 seconds\n", "Fitness: 0.001345\n", "Inlier RMSE: 0.372878 meters\n", "\n", "Ending Cell 16-----------------------------------------\n", "Executing Cell 17--------------------------------------\n", "\n", "FINAL TRANSFORMATION\n", "Transformation matrix:\n", "[[ 9.99999465e-01 -1.03433702e-03  3.23614957e-05  5.18403436e+03]\n", " [ 1.03427684e-03  9.99997832e-01  1.80740805e-03 -4.39702750e+02]\n", " [-3.42308946e-05 -1.80737361e-03  9.99998366e-01  9.07308147e+03]\n", " [ 0.00000000e+00  0.00000000e+00  0.00000000e+00  1.00000000e+00]]\n", "\n", "Translation: [5184.034, -439.703, 9073.081] meters\n", "Rotation (approx): X=-0.10°, Y=0.00°, Z=0.06°\n", "\n", "Ending Cell 17-----------------------------------------\n", "Executing Cell 18--------------------------------------\n", "Ending Cell 18-----------------------------------------\n", "Executing Cell 19--------------------------------------\n", "Saved visualization to: ../../../data/output_runs/icp_alignment/ransac/icp_alignment_visualization_ransac.png\n", "\n", "<Figure size 1500x1200 with 4 Axes>\n", "Ending Cell 19-----------------------------------------\n", "Executing Cell 20--------------------------------------\n", "Ending Cell 20-----------------------------------------\n", "Executing Cell 21--------------------------------------\n", "\n", "ALIGNMENT ERROR ANALYSIS\n", "Finding nearest neighbors...\n", "\n", "\n", "ERROR METRICS\n", "RMSE: 10.7970 meters\n", "Max deviation: 66.0760 meters\n", "Mean deviation: 8.9553 meters\n", "Std deviation: 6.0314 meters\n", "Median deviation: 7.7567 meters\n", "95th percentile: 19.3075 meters\n", "99th percentile: 30.9640 meters\n", "\n", "Ending Cell 21-----------------------------------------\n", "Executing Cell 22--------------------------------------\n", "Saved error analysis to: ../../../data/output_runs/icp_alignment/ransac/icp_error_analysis_ransac.png\n", "\n", "<Figure size 1500x500 with 2 Axes>\n", "Ending Cell 22-----------------------------------------\n", "Executing Cell 23--------------------------------------\n", "\n", "SAVING RESULTS\n", "Saved aligned point cloud: ../../../data/output_runs/icp_alignment/ransac/icp_aligned_ransac.ply\n", "Saved transformation matrix: ../../../data/output_runs/icp_alignment/ransac/icp_transformation_ransac.npy\n", "Saved metrics: ../../../data/output_runs/icp_alignment/ransac/icp_metrics_ransac.json\n", "\n", "All results saved to: ../../../data/output_runs/icp_alignment/ransac\n", "\n", "Ending Cell 23-----------------------------------------\n", "Executing Cell 24--------------------------------------\n", "\n", "============================================================\n", "ICP ALIGNMENT SUMMARY\n", "============================================================\n", "Site: trino_enel\n", "Ground method: ransac\n", "Processing time: 6.83 seconds\n", "\n", "Alignment Quality:\n", "  RMSE: 10.7970 meters\n", "  Max deviation: 66.0760 meters\n", "  ICP fitness: 0.001345\n", "  ICP inlier RMSE: 0.372878 meters\n", "\n", "Alignment Success: POOR\n", "============================================================\n", "\n", "Ending Cell 24-----------------------------------------\n", "Completed ICP + RANSAC with params: {'voxel_size': 0.02, 'max_iterations': 100, 'tolerance': 1e-07}\n", "Input Notebook:  01_icp_alignment.ipynb\n", "Output Notebook: ../../../data/output_runs/alignment_testing/ransac_pmf/01_icp_alignment_ransac_pmf.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "No handler found for comm target 'dash'\n", "ICP ALIGNMENT - RANSAC_PMF\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment/ransac_pmf\n", "Timestamp: 2025-07-06 13:17:19\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Loading point clouds...\n", "Drone scan (non-ground): ../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "Drone exists: True\n", "IFC point cloud: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "IFC exists: True\n", "\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Loaded drone scan: 22,689 points\n", "Loaded IFC model: 1,359,240 points\n", "\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "\n", "INITIAL POINT CLOUD STATISTICS\n", "\n", "Drone scan (ground truth):\n", "  Points: 22,689\n", "  X range: [435221.67, 436794.15]\n", "  Y range: [5010816.92, 5012539.06]\n", "  Z range: [0.05, 13.14]\n", "\n", "IFC model (to be aligned):\n", "  Points: 1,359,240\n", "  X range: [435267.17, 436719.98]\n", "  Y range: [5010900.69, 5012462.43]\n", "  Z range: [152.87, 161.66]\n", "\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "\n", "COORDINATE SYSTEM ANALYSIS\n", "Drone center: [436046.70, 5011706.73, 1.79]\n", "IFC center:   [435986.35, 5011746.88, 157.34]\n", "Offset:       [60.36, -40.15, -155.55]\n", "\n", "Offset magnitude: 171.61 meters\n", "\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "\n", "Z-SHIFT CORRECTION\n", "Drone Z median: 1.46 m\n", "IFC Z median:   157.34 m\n", "Z-shift needed: -155.88 m\n", "Applied Z-shift of -155.88 m to IFC point cloud\n", "\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "\n", "Z-SHIFT VERIFICATION\n", "IFC Z median after shift: 1.46 m\n", "Remaining Z difference: 0.00 m\n", "Z-shift successful: True\n", "\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "\n", "ICP ALIGNMENT SETUP\n", "Source (IFC): 1,359,240 points\n", "Target (Drone): 22,689 points\n", "Max iterations: 75\n", "Tolerance: 1e-06\n", "\n", "Ending Cell 14-----------------------------------------\n", "Executing Cell 15--------------------------------------\n", "\n", "COARSE ALIGNMENT\n", "Source center: [435986.35, 5011746.88, 1.46]\n", "Target center: [436046.70, 5011706.73, 1.79]\n", "Coarse translation: [60.36, -40.15, 0.33]\n", "Applied coarse alignment\n", "\n", "Ending Cell 15-----------------------------------------\n", "Executing Cell 16--------------------------------------\n", "\n", "FINE ALIGNMENT (ICP)\n", "Running ICP registration...\n", "\n", "ICP completed in 2.45 seconds\n", "Fitness: 0.001223\n", "Inlier RMSE: 0.369786 meters\n", "\n", "Ending Cell 16-----------------------------------------\n", "Executing Cell 17--------------------------------------\n", "\n", "FINAL TRANSFORMATION\n", "Transformation matrix:\n", "[[ 9.99999443e-01 -1.02788533e-03 -2.39159646e-04  5.15167320e+03]\n", " [ 1.02805301e-03  9.99999225e-01  7.02041646e-04 -4.44281194e+02]\n", " [ 2.38437842e-04 -7.02287123e-04  9.99999725e-01  3.41579728e+03]\n", " [ 0.00000000e+00  0.00000000e+00  0.00000000e+00  1.00000000e+00]]\n", "\n", "Translation: [5151.673, -444.281, 3415.797] meters\n", "Rotation (approx): X=-0.04°, Y=-0.01°, Z=0.06°\n", "\n", "Ending Cell 17-----------------------------------------\n", "Executing Cell 18--------------------------------------\n", "Ending Cell 18-----------------------------------------\n", "Executing Cell 19--------------------------------------\n", "Saved visualization to: ../../../data/output_runs/icp_alignment/ransac_pmf/icp_alignment_visualization_ransac_pmf.png\n", "\n", "<Figure size 1500x1200 with 4 Axes>\n", "Ending Cell 19-----------------------------------------\n", "Executing Cell 20--------------------------------------\n", "Ending Cell 20-----------------------------------------\n", "Executing Cell 21--------------------------------------\n", "\n", "ALIGNMENT ERROR ANALYSIS\n", "Finding nearest neighbors...\n", "\n", "\n", "ERROR METRICS\n", "RMSE: 10.8025 meters\n", "Max deviation: 66.0628 meters\n", "Mean deviation: 8.9652 meters\n", "Std deviation: 6.0266 meters\n", "Median deviation: 7.7696 meters\n", "95th percentile: 19.3052 meters\n", "99th percentile: 30.9626 meters\n", "\n", "Ending Cell 21-----------------------------------------\n", "Executing Cell 22--------------------------------------\n", "Saved error analysis to: ../../../data/output_runs/icp_alignment/ransac_pmf/icp_error_analysis_ransac_pmf.png\n", "\n", "<Figure size 1500x500 with 2 Axes>\n", "Ending Cell 22-----------------------------------------\n", "Executing Cell 23--------------------------------------\n", "\n", "SAVING RESULTS\n", "Saved aligned point cloud: ../../../data/output_runs/icp_alignment/ransac_pmf/icp_aligned_ransac_pmf.ply\n", "Saved transformation matrix: ../../../data/output_runs/icp_alignment/ransac_pmf/icp_transformation_ransac_pmf.npy\n", "Saved metrics: ../../../data/output_runs/icp_alignment/ransac_pmf/icp_metrics_ransac_pmf.json\n", "\n", "All results saved to: ../../../data/output_runs/icp_alignment/ransac_pmf\n", "\n", "Ending Cell 23-----------------------------------------\n", "Executing Cell 24--------------------------------------\n", "\n", "============================================================\n", "ICP ALIGNMENT SUMMARY\n", "============================================================\n", "Site: trino_enel\n", "Ground method: ransac_pmf\n", "Processing time: 2.45 seconds\n", "\n", "Alignment Quality:\n", "  RMSE: 10.8025 meters\n", "  Max deviation: 66.0628 meters\n", "  ICP fitness: 0.001223\n", "  ICP inlier RMSE: 0.369786 meters\n", "\n", "Alignment Success: POOR\n", "============================================================\n", "\n", "Ending Cell 24-----------------------------------------\n", "Completed ICP + RANSAC_PMF with params: {'voxel_size': 0.075, 'max_iterations': 75, 'tolerance': 1e-06}\n"]}], "source": ["def run_icp_tests():\n", "    \"\"\"Run ICP tests with method-specific parameters\"\"\"\n", "    print(\"Running ICP tests...\")\n", "    tests = {\n", "        \"csf\": {\n", "            \"voxel_size\": 0.05,\n", "            \"max_iterations\": 30,\n", "            \"tolerance\": 1e-6\n", "        },\n", "        \"pmf\": {\n", "            \"voxel_size\": 0.1,\n", "            \"max_iterations\": 50,\n", "            \"tolerance\": 1e-5\n", "        },\n", "        \"ransac\": {\n", "            \"voxel_size\": 0.02,\n", "            \"max_iterations\": 100,\n", "            \"tolerance\": 1e-7\n", "        },\n", "        \"ransac_pmf\": {\n", "            \"voxel_size\": 0.075,\n", "            \"max_iterations\": 75,\n", "            \"tolerance\": 1e-6\n", "        }\n", "    }\n", "    \n", "    for method, params in tests.items():\n", "        !papermill 01_icp_alignment.ipynb \\\n", "            {output_base}/{method}/01_icp_alignment_{method}.ipynb \\\n", "            -p ground_method {method} \\\n", "            -p site_name \"trino_enel\" \\\n", "            -p voxel_size {params[\"voxel_size\"]} \\\n", "            -p icp_max_iterations {params[\"max_iterations\"]} \\\n", "            -p icp_tolerance {params[\"tolerance\"]} \\\n", "            --log-output \\\n", "            --kernel pytorch-geo-dev\n", "        \n", "        print(f\"Completed ICP + {method.upper()} with params: {params}\")\n", "\n", "# Run ICP tests\n", "run_icp_tests()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.5. Corrected ICP Alignment Tests"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input Notebook:  02_icp_alignment_corrected.ipynb\n", "Output Notebook: ../../../data/output_runs/alignment_testing/csf/02_icp_alignment_corrected_csf.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "No handler found for comm target 'dash'\n", "ICP ALIGNMENT WITH COORDINATE CORRECTION - CSF\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment_corrected/csf\n", "Coordinate correction: Enabled\n", "Timestamp: 2025-07-09 19:49:33\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "total 0\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul  5 21:31 \u001b[34madvanced_ifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  9 13:03 \u001b[34maligned_coordinates\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 17:11 \u001b[34manalysis\u001b[m\u001b[m\n", "drwxr-xr-x@  4 <USER>  <GROUP>   128B Jul  3 20:07 \u001b[34mdenoising\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  4 15:07 \u001b[34mground_segmentation\u001b[m\u001b[m\n", "drwxr-xr-x@ 12 <USER>  <GROUP>   384B Jul  4 16:34 \u001b[34mifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  9 <USER>  <GROUP>   288B Jul  6 13:15 \u001b[34mifc_pointclouds\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 16:35 \u001b[34mvalidation\u001b[m\u001b[m\n", "\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Using coordinate-corrected point clouds\n", "  Drone (corrected): ../../../data/processed/trino_enel/aligned_coordinates/trino_enel_drone_csf_corrected.ply\n", "  IFC (corrected): ../../../data/processed/trino_enel/aligned_coordinates/trino_enel_ifc_corrected.ply\n", "\n", "Loading point clouds...\n", "Drone exists: True\n", "IFC exists: True\n", "\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "Loaded drone scan: 13,848 points\n", "Loaded IFC model: 1,359,240 points\n", "\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "\n", "POINT CLOUD STATISTICS\n", "\n", "Drone scan (ground truth):\n", "  Points: 13,848\n", "  X range: [435223.72, 436794.15]\n", "  Y range: [5010816.92, 5012539.06]\n", "  Z range: [1.17, 13.14]\n", "\n", "IFC model (to be aligned):\n", "  Points: 1,359,240\n", "  X range: [435305.52, 436758.33]\n", "  Y range: [5010837.09, 5012398.83]\n", "  Z range: [-2.00, 6.79]\n", "\n", "INITIAL COORDINATE ANALYSIS\n", "Drone center: [436024.70, 5011683.28, 2.46]\n", "IFC center:   [436024.70, 5011683.28, 2.47]\n", "Initial offset: [-0.00, -0.00, -0.00]\n", "Offset magnitude: 0.00 meters\n", "Good coordinate alignment (offset: 0.00m)\n", "\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "\n", "PREPARING POINT CLOUDS FOR ICP\n", "Source (IFC): 1,359,240 points\n", "Target (Drone): 13,848 points\n", "\n", "DOWNSAMPLING (voxel size: 0.02m)\n", "After downsampling - Source: 1,331,779, Target: 13,848\n", "\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "\n", "RUNNING ICP ALIGNMENT\n", "Max iterations: 100\n", "Tolerance: 1e-07\n", "\n", "\n", "ICP COMPLETED in 8.06 seconds\n", "Fitness: 0.024501\n", "Inlier RMSE: 1.495502\n", "Correspondence set size: 32630\n", "\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "\n", "ALIGNMENT RESULTS\n", "\n", "RMSE: 19.366 m\n", "Mean distance: 14.490 m\n", "Max distance: 97.300 m\n", "ICP Fitness: 0.024501\n", "ICP Inlier RMSE: 1.495502 m\n", "\n", "TRANSFORMATION MATRIX:\n", "[[ 9.99998992e-01  6.01488452e-04 -1.28625099e-03 -3.01232278e+03]\n", " [-5.99378166e-04  9.99998475e-01  1.64040527e-03  2.68758354e+02]\n", " [ 1.28723571e-03 -1.63963266e-03  9.99997827e-01  7.65604013e+03]\n", " [ 0.00000000e+00  0.00000000e+00  0.00000000e+00  1.00000000e+00]]\n", "\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "\n", "SAVING RESULTS\n", "\n", "Aligned point cloud: ../../../data/output_runs/icp_alignment_corrected/csf/aligned_ifc_csf.ply\n", "Transformation matrix: ../../../data/output_runs/icp_alignment_corrected/csf/transformation_matrix_csf.npy\n", "Metrics: ../../../data/output_runs/icp_alignment_corrected/csf/corrected_icp_metrics_csf.json\n", "\n", "CORRECTED ICP ALIGNMENT COMPLETED\n", "Expected improvement: 6.64-19.37m → 19.366m\n", "NEEDS WORK: Still high RMSE, coordinate correction may be needed\n", "\n", "Ending Cell 14-----------------------------------------\n", "Completed Corrected ICP + CSF with params: {'voxel_size': 0.02, 'max_iterations': 100, 'tolerance': 1e-07}\n", "Input Notebook:  02_icp_alignment_corrected.ipynb\n", "Output Notebook: ../../../data/output_runs/alignment_testing/pmf/02_icp_alignment_corrected_pmf.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "No handler found for comm target 'dash'\n", "ICP ALIGNMENT WITH COORDINATE CORRECTION - PMF\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment_corrected/pmf\n", "Coordinate correction: Enabled\n", "Timestamp: 2025-07-09 19:49:47\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "total 0\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul  5 21:31 \u001b[34madvanced_ifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  9 13:03 \u001b[34maligned_coordinates\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 17:11 \u001b[34manalysis\u001b[m\u001b[m\n", "drwxr-xr-x@  4 <USER>  <GROUP>   128B Jul  3 20:07 \u001b[34mdenoising\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  4 15:07 \u001b[34mground_segmentation\u001b[m\u001b[m\n", "drwxr-xr-x@ 12 <USER>  <GROUP>   384B Jul  4 16:34 \u001b[34mifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  9 <USER>  <GROUP>   288B Jul  6 13:15 \u001b[34mifc_pointclouds\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 16:35 \u001b[34mvalidation\u001b[m\u001b[m\n", "\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Corrected files not found - using original point clouds\n", "  Run 01_coordinate_correction.ipynb first for better results\n", "  Drone (original): ../../data/processed/trino_enel/ground_segmentation/pmf/trino_enel_nonground.ply\n", "  IFC (original): ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "Loading point clouds...\n", "Drone exists: False\n", "IFC exists: False\n", "\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "Error: Required point cloud files not found!\n", "  Missing: ../../data/processed/trino_enel/ground_segmentation/pmf/trino_enel_nonground.ply\n", "  Missing: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "Ending Cell 8------------------------------------------\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/bin/papermill\", line 8, in <module>\n", "    sys.exit(papermill())\n", "             ^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 1442, in __call__\n", "    return self.main(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 1363, in main\n", "    rv = self.invoke(ctx)\n", "         ^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 1226, in invoke\n", "    return ctx.invoke(self.callback, **ctx.params)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 794, in invoke\n", "    return callback(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/decorators.py\", line 34, in new_func\n", "    return f(get_current_context(), *args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/papermill/cli.py\", line 235, in papermill\n", "    execute_notebook(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/papermill/execute.py\", line 131, in execute_notebook\n", "    raise_for_execution_errors(nb, output_path)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/papermill/execute.py\", line 251, in raise_for_execution_errors\n", "    raise error\n", "papermill.exceptions.PapermillExecutionError: \n", "---------------------------------------------------------------------------\n", "Exception encountered at \"In [6]\":\n", "---------------------------------------------------------------------------\n", "FileNotFoundError                         <PERSON> (most recent call last)\n", "Cell In[6], line 22\n", "     20 if not ifc_file.exists():\n", "     21     print(f\"  Missing: {ifc_file}\")\n", "---> 22 raise FileNotFoundError(\"Missing point cloud files\")\n", "\n", "FileNotFoundError: Missing point cloud files\n", "\n", "Completed Corrected ICP + PMF with params: {'voxel_size': 0.05, 'max_iterations': 75, 'tolerance': 1e-06}\n", "Input Notebook:  02_icp_alignment_corrected.ipynb\n", "Output Notebook: ../../../data/output_runs/alignment_testing/ransac/02_icp_alignment_corrected_ransac.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "No handler found for comm target 'dash'\n", "ICP ALIGNMENT WITH COORDINATE CORRECTION - RANSAC\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment_corrected/ransac\n", "Coordinate correction: Enabled\n", "Timestamp: 2025-07-09 19:49:50\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "total 0\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul  5 21:31 \u001b[34madvanced_ifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  9 13:03 \u001b[34maligned_coordinates\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 17:11 \u001b[34manalysis\u001b[m\u001b[m\n", "drwxr-xr-x@  4 <USER>  <GROUP>   128B Jul  3 20:07 \u001b[34mdenoising\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  4 15:07 \u001b[34mground_segmentation\u001b[m\u001b[m\n", "drwxr-xr-x@ 12 <USER>  <GROUP>   384B Jul  4 16:34 \u001b[34mifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  9 <USER>  <GROUP>   288B Jul  6 13:15 \u001b[34mifc_pointclouds\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 16:35 \u001b[34mvalidation\u001b[m\u001b[m\n", "\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Corrected files not found - using original point clouds\n", "  Run 01_coordinate_correction.ipynb first for better results\n", "  Drone (original): ../../data/processed/trino_enel/ground_segmentation/ransac/trino_enel_nonground.ply\n", "  IFC (original): ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "Loading point clouds...\n", "Drone exists: False\n", "IFC exists: False\n", "\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "Error: Required point cloud files not found!\n", "  Missing: ../../data/processed/trino_enel/ground_segmentation/ransac/trino_enel_nonground.ply\n", "  Missing: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "Ending Cell 8------------------------------------------\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/bin/papermill\", line 8, in <module>\n", "    sys.exit(papermill())\n", "             ^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 1442, in __call__\n", "    return self.main(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 1363, in main\n", "    rv = self.invoke(ctx)\n", "         ^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 1226, in invoke\n", "    return ctx.invoke(self.callback, **ctx.params)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 794, in invoke\n", "    return callback(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/decorators.py\", line 34, in new_func\n", "    return f(get_current_context(), *args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/papermill/cli.py\", line 235, in papermill\n", "    execute_notebook(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/papermill/execute.py\", line 131, in execute_notebook\n", "    raise_for_execution_errors(nb, output_path)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/papermill/execute.py\", line 251, in raise_for_execution_errors\n", "    raise error\n", "papermill.exceptions.PapermillExecutionError: \n", "---------------------------------------------------------------------------\n", "Exception encountered at \"In [6]\":\n", "---------------------------------------------------------------------------\n", "FileNotFoundError                         <PERSON> (most recent call last)\n", "Cell In[6], line 22\n", "     20 if not ifc_file.exists():\n", "     21     print(f\"  Missing: {ifc_file}\")\n", "---> 22 raise FileNotFoundError(\"Missing point cloud files\")\n", "\n", "FileNotFoundError: Missing point cloud files\n", "\n", "Completed Corrected ICP + RANSAC with params: {'voxel_size': 0.02, 'max_iterations': 100, 'tolerance': 1e-07}\n", "Input Notebook:  02_icp_alignment_corrected.ipynb\n", "Output Notebook: ../../../data/output_runs/alignment_testing/ransac_pmf/02_icp_alignment_corrected_ransac_pmf.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "No handler found for comm target 'dash'\n", "ICP ALIGNMENT WITH COORDINATE CORRECTION - RANSAC_PMF\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment_corrected/ransac_pmf\n", "Coordinate correction: Enabled\n", "Timestamp: 2025-07-09 19:49:54\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "total 0\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul  5 21:31 \u001b[34madvanced_ifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  9 13:03 \u001b[34maligned_coordinates\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 17:11 \u001b[34manalysis\u001b[m\u001b[m\n", "drwxr-xr-x@  4 <USER>  <GROUP>   128B Jul  3 20:07 \u001b[34mdenoising\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  4 15:07 \u001b[34mground_segmentation\u001b[m\u001b[m\n", "drwxr-xr-x@ 12 <USER>  <GROUP>   384B Jul  4 16:34 \u001b[34mifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  9 <USER>  <GROUP>   288B Jul  6 13:15 \u001b[34mifc_pointclouds\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 16:35 \u001b[34mvalidation\u001b[m\u001b[m\n", "\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Corrected files not found - using original point clouds\n", "  Run 01_coordinate_correction.ipynb first for better results\n", "  Drone (original): ../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "  IFC (original): ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "Loading point clouds...\n", "Drone exists: False\n", "IFC exists: False\n", "\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "Error: Required point cloud files not found!\n", "  Missing: ../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "  Missing: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "Ending Cell 8------------------------------------------\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/bin/papermill\", line 8, in <module>\n", "    sys.exit(papermill())\n", "             ^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 1442, in __call__\n", "    return self.main(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 1363, in main\n", "    rv = self.invoke(ctx)\n", "         ^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 1226, in invoke\n", "    return ctx.invoke(self.callback, **ctx.params)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 794, in invoke\n", "    return callback(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/decorators.py\", line 34, in new_func\n", "    return f(get_current_context(), *args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/papermill/cli.py\", line 235, in papermill\n", "    execute_notebook(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/papermill/execute.py\", line 131, in execute_notebook\n", "    raise_for_execution_errors(nb, output_path)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/papermill/execute.py\", line 251, in raise_for_execution_errors\n", "    raise error\n", "papermill.exceptions.PapermillExecutionError: \n", "---------------------------------------------------------------------------\n", "Exception encountered at \"In [6]\":\n", "---------------------------------------------------------------------------\n", "FileNotFoundError                         <PERSON> (most recent call last)\n", "Cell In[6], line 22\n", "     20 if not ifc_file.exists():\n", "     21     print(f\"  Missing: {ifc_file}\")\n", "---> 22 raise FileNotFoundError(\"Missing point cloud files\")\n", "\n", "FileNotFoundError: Missing point cloud files\n", "\n", "Completed Corrected ICP + RANSAC_PMF with params: {'voxel_size': 0.075, 'max_iterations': 75, 'tolerance': 1e-06}\n"]}], "source": ["# Test corrected ICP alignment with all ground segmentation methods\n", "def run_corrected_icp_tests():\n", "    \"\"\"Run corrected ICP alignment tests for all ground segmentation methods.\"\"\"\n", "    \n", "    output_base = \"../../../data/output_runs/alignment_testing\"\n", "    \n", "    # Test parameters for each ground segmentation method\n", "    tests = {\n", "        \"csf\": {\n", "            \"voxel_size\": 0.02,\n", "            \"max_iterations\": 100,\n", "            \"tolerance\": 1e-7\n", "        },\n", "        \"pmf\": {\n", "            \"voxel_size\": 0.05,\n", "            \"max_iterations\": 75,\n", "            \"tolerance\": 1e-6\n", "        },\n", "        \"ransac\": {\n", "            \"voxel_size\": 0.02,\n", "            \"max_iterations\": 100,\n", "            \"tolerance\": 1e-7\n", "        },\n", "        \"ransac_pmf\": {\n", "            \"voxel_size\": 0.075,\n", "            \"max_iterations\": 75,\n", "            \"tolerance\": 1e-6\n", "        }\n", "    }\n", "    \n", "    for method, params in tests.items():\n", "        !papermill 02_icp_alignment_corrected.ipynb \\\n", "            {output_base}/{method}/02_icp_alignment_corrected_{method}.ipynb \\\n", "            -p ground_method {method} \\\n", "            -p site_name \"trino_enel\" \\\n", "            -p voxel_size {params[\"voxel_size\"]} \\\n", "            -p icp_max_iterations {params[\"max_iterations\"]} \\\n", "            -p icp_tolerance {params[\"tolerance\"]} \\\n", "            --log-output \\\n", "            --kernel pytorch-geo-dev\n", "        \n", "        print(f\"Completed Corrected ICP + {method.upper()} with params: {params}\")\n", "\n", "# Run corrected ICP tests\n", "run_corrected_icp_tests()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Neural Network Alignment with Custom Parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_neural_tests():\n", "    \"\"\"Run NN tests with method-specific parameters\"\"\"\n", "    tests = {\n", "        \"csf\": {\n", "            \"learning_rate\": 0.001,\n", "            \"batch_size\": 32\n", "        },\n", "        \"pmf\": {\n", "            \"learning_rate\": 0.0005,\n", "            \"batch_size\": 64\n", "        },\n", "        \"ransac\": {\n", "            \"learning_rate\": 0.002,\n", "            \"batch_size\": 16\n", "        },\n", "        \"ransac_pmf\": {\n", "            \"learning_rate\": 0.0015,\n", "            \"batch_size\": 48\n", "        }\n", "    }\n", "    \n", "    for method, params in tests.items():\n", "        !papermill 02_neural_network_alignment.ipynb \\\n", "            {output_base}/{method}/02_neural_alignment_{method}.ipynb \\\n", "            -p ground_method {method} \\\n", "            -p site_name \"trino_enel\" \\\n", "            -p learning_rate {params[\"learning_rate\"]} \\\n", "            -p batch_size {params[\"batch_size\"]} \\\n", "            --log-output \\\n", "            --kernel pytorch-geo-dev\n", "        \n", "        print(f\"Completed Neural + {method.upper()} with params: {params}\")\n", "\n", "# Run neural network tests\n", "run_neural_tests()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Hybrid Alignment with Custom Parameter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_hybrid_tests():\n", "    \"\"\"Run hybrid tests with combined parameters\"\"\"\n", "    tests = {\n", "        \"csf\": {\n", "            \"nn_learning_rate\": 0.001,\n", "            \"icp_voxel_size\": 0.05,\n", "            \"icp_iterations\": 30\n", "        },\n", "        \"pmf\": {\n", "            \"nn_learning_rate\": 0.0005,\n", "            \"icp_voxel_size\": 0.1,\n", "            \"icp_iterations\": 50\n", "        },\n", "        \"ransac\": {\n", "            \"nn_learning_rate\": 0.002,\n", "            \"icp_voxel_size\": 0.02,\n", "            \"icp_iterations\": 100\n", "        },\n", "        \"ransac_pmf\": {\n", "            \"nn_learning_rate\": 0.0015,\n", "            \"icp_voxel_size\": 0.075,\n", "            \"icp_iterations\": 75\n", "        }\n", "    }\n", "    \n", "    for method, params in tests.items():\n", "        !papermill 03_hybrid_alignment.ipynb \\\n", "            {output_base}/alignment_testing/{method}/03_hybrid_alignment_{method}.ipynb \\\n", "            -p ground_method {method} \\\n", "            -p site_name \"trino_enel\" \\\n", "            -p learning_rate {params[\"nn_learning_rate\"]} \\\n", "            -p voxel_size {params[\"icp_voxel_size\"]} \\\n", "            -p icp_max_iterations {params[\"icp_iterations\"]} \\\n", "            --log-output \\\n", "            --kernel pytorch-geo-dev\n", "        \n", "        print(f\"Completed Hybrid + {method.upper()} with params: {params}\")\n", "\n", "# Run hybrid tests\n", "run_hybrid_tests()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Results Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "\n", "# When run via Papermill, __file__ is not available; use cwd fallback\n", "notebooks_root = Path.cwd().resolve().parents[0]  # adjust if needed\n", "\n", "# Add the notebooks root (which contains 'shared') to sys.path\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "from shared.config import get_data_path, find_latest_file\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"tags": ["parameters"]}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Configuration:\n", "INFO:__main__:  Ground Method: csf\n", "INFO:__main__:  Source File: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "INFO:__main__:  Target File: GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "INFO:__main__:  Output Dir: ../../data/output_runs/alignment_testing/csf\n", "INFO:__main__:  MLflow Run: neural_network_csf_trino_enel\n"]}], "source": ["# Papermill parameters - will be overridden during execution\n", "ground_method = \"csf\"  # Default: csf, pmf, ransac, ransac_pmf\n", "site_name = \"trino_enel\"\n", "project_type = \"trino_enel\"\n", "\n", "# Auto-discover ground segmentation file based on method\n", "import os\n", "from pathlib import Path\n", "\n", "def get_ground_file(method, base_path=\"../../data/processed/trino_enel/ground_segmentation\"):\n", "    \"\"\"Get ground segmentation file for specified method\"\"\"\n", "    \n", "    base_path = Path(base_path)\n", "    method_path = base_path / method\n", "    \n", "    # List of common filename patterns to check - updated for actual file structure\n", "    # Use NON-GROUND points for infrastructure alignment (structures, not terrain)\n", "    patterns = [\n", "        \"trino_enel_nonground.ply\",  # Exact match for your files - STRUCTURES\n", "        \"*nonground*.ply\",\n", "        \"*nonground*.las\"\n", "    ]\n", "    \n", "    if not method_path.exists():\n", "        logger.info(f\"Warning: Method path does not exist: {method_path}\")\n", "        return None\n", "    \n", "    for pattern in patterns:\n", "        matching_files = list(method_path.glob(pattern))\n", "        if matching_files:\n", "            return matching_files[0]  # Return first match as Path object\n", "    \n", "    # Fallback to default naming\n", "    fallback_file = method_path / f\"{method}_ground_points.ply\"\n", "    return fallback_file if fallback_file.exists() else None\n", "\n", "# Set source file based on ground method\n", "source_file = get_ground_file(ground_method)\n", "target_file = \"GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\"  # Data-driven IFC point cloud file\n", "\n", "# Neural Network parameters\n", "num_points = 1024\n", "batch_size = 32\n", "epochs = 100\n", "learning_rate = 0.001\n", "validation_split = 0.2\n", "\n", "# Output configuration\n", "output_dir = f\"../../data/output_runs/alignment_testing/{ground_method}\"\n", "mlflow_experiment_name = f\"alignment_neural_network_{ground_method}\"\n", "mlflow_run_name = f\"neural_network_{ground_method}_{site_name}\"  # Neural Network-specific run name\n", "\n", "# Execution flags\n", "enable_visualization = True\n", "save_intermediate = True\n", "save_results = True\n", "\n", "# Configure logging\n", "import logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "# Create output directory\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "logger.info(f\"Configuration:\")\n", "logger.info(f\"  Ground Method: {ground_method}\")\n", "logger.info(f\"  Source File: {source_file}\")\n", "logger.info(f\"  Target File: {target_file}\")\n", "logger.info(f\"  Output Dir: {output_dir}\")\n", "logger.info(f\"  MLflow Run: {mlflow_run_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## File Path Validation"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:File Validation for csf:\n", "INFO:__main__:  Source: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "INFO:__main__:  Target: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "INFO:__main__:\n", "File validation passed - ready to proceed\n", "INFO:__main__:Final source file: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "INFO:__main__:Final target file: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n"]}], "source": ["# File Path Validation - Validate that required files exist\n", "import os\n", "from pathlib import Path\n", "\n", "def validate_file_paths(source_file, target_file, ground_method):\n", "    \"\"\"Validate that required files exist\"\"\"\n", "    \n", "    # Define base paths\n", "    base_path = Path(\"../../data/processed/trino_enel\")\n", "    ground_base = base_path / \"ground_segmentation\"\n", "    ifc_base = base_path / \"ifc_pointclouds\"\n", "    \n", "    # Check for source file in multiple locations\n", "    if isinstance(source_file, Path):\n", "        source_paths = [source_file]\n", "    else:\n", "        source_paths = [\n", "            ground_base / source_file,\n", "            ground_base / ground_method / source_file,\n", "            base_path / \"ground_segmentation\" / source_file\n", "        ]\n", "    \n", "    source_found = None\n", "    for path in source_paths:\n", "        if path.exists():\n", "            source_found = path\n", "            break\n", "    \n", "    # Check target file\n", "    target_paths = [\n", "        ifc_base / target_file,\n", "        base_path / \"ifc_pointclouds\" / target_file,\n", "        base_path / \"ifc_metadata\" / target_file,  # Also check IFC metadata folder\n", "        base_path / target_file\n", "    ]\n", "    \n", "    target_found = None\n", "    for path in target_paths:\n", "        if path.exists():\n", "            target_found = path\n", "            break\n", "    \n", "    # Report results\n", "    logger.info(f\"File Validation for {ground_method}:\")\n", "    \n", "    if source_found:\n", "        logger.info(f\"  Source: {source_found}\")\n", "    else:\n", "        logger.info(f\"  Source: {source_file} not found\")\n", "        logger.info(f\"     Searched in:\")\n", "        for path in source_paths:\n", "            logger.info(f\"       {path}\")\n", "        \n", "        # List available files\n", "        logger.info(f\"     Available ground files:\")\n", "        if ground_base.exists():\n", "            for method_dir in ground_base.iterdir():\n", "                if method_dir.is_dir():\n", "                    for file in method_dir.glob(\"*.ply\"):\n", "                        logger.info(f\"       {method_dir.name}/{file.name}\")\n", "    \n", "    if target_found:\n", "        logger.info(f\"  Target: {target_found}\")\n", "    else:\n", "        logger.info(f\"  Target: {target_file} not found\")\n", "        logger.info(f\"     Searched in:\")\n", "        for path in target_paths:\n", "            logger.info(f\"       {path}\")\n", "    \n", "    # Update file paths if found\n", "    if source_found:\n", "        globals()['source_file'] = str(source_found)\n", "    if target_found:\n", "        globals()['target_file'] = str(target_found)\n", "    \n", "    return source_found is not None and target_found is not None\n", "\n", "# Run validation\n", "validation_passed = validate_file_paths(source_file, target_file, ground_method)\n", "\n", "if not validation_passed:\n", "    logger.info(\"\\nFILE VALIDATION FAILED\")\n", "    logger.info(\"Please check file paths before proceeding\")\n", "else:\n", "    logger.info(f\"\\nFile validation passed - ready to proceed\")\n", "    logger.info(f\"Final source file: {source_file}\")\n", "    logger.info(f\"Final target file: {target_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Neural Network-Based Point Cloud Alignment\n", "\n", "This notebook implements deep learning approaches for point cloud alignment using neural networks. It provides comprehensive implementations with modular execution cells for clarity and detailed analysis of neural network performance.\n", "\n", "**Stage**: Alignment  \n", "**Input Data**: Source and target point clouds  \n", "**Output**: Aligned point cloud with learned transformation  \n", "**Method**: Neural network-based registration (PointNet-style)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Environment Setup**: Import libraries and configure TensorFlow\n", "2. **Data Loading**: Load and prepare training/test datasets\n", "3. **Network Architecture**: Define neural network models\n", "4. **Training Pipeline**: Train alignment networks\n", "5. **Evaluation**: Performance metrics and quality assessment\n", "6. **Visualization**: Comprehensive alignment results\n", "7. **Export**: Save trained models and aligned point clouds"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Setup\n", "\n", "Configure the environment with TensorFlow and required libraries for neural network alignment."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "#!pip install tensorflow open3d matplotlib laspy transforms3d scipy pandas mlflow"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Neural Network Alignment Environment Initialized\n", "INFO:__main__:TensorFlow version: 2.19.0\n", "INFO:__main__:Open3D version: 0.19.0\n", "INFO:__main__:Analysis Date: 2025-07-05 18:53:51\n"]}], "source": ["# Import libraries\n", "import tensorflow as tf\n", "import numpy as np\n", "import os\n", "import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "import laspy\n", "import logging\n", "import time\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "import pandas as pd\n", "import json\n", "import transforms3d.euler as t3d\n", "\n", "import mlflow\n", "import mlflow.tensorflow\n", "import mlflow.keras\n", "MLFLOW_AVAILABLE = True\n", "\n", "# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "tf.random.set_seed(42)\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "logger.info(\"Neural Network Alignment Environment Initialized\")\n", "logger.info(f\"TensorFlow version: {tf.__version__}\")\n", "logger.info(f\"Open3D version: {o3d.__version__}\")\n", "logger.info(f\"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration Parameters\n", "\n", "Define neural network parameters and training configuration."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Project: ENEL/Trino\n", "INFO:__main__:Input path: ../../data/ENEL/Trino/preprocessing\n", "INFO:__main__:Output path: ../../data/ENEL/Trino/alignment\n", "INFO:__main__:Models path: ../../data/ENEL/Trino/alignment/models\n"]}], "source": ["class NeuralNetworkConfig:\n", "    \"\"\"Configuration parameters for neural network alignment.\"\"\"\n", "    \n", "    # Data paths\n", "    PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "    PROJECT_NAME = \"Trino\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, Giorgio | USA: <PERSON>, <PERSON><PERSON><PERSON>, RES\n", "    \n", "    # Network Parameters\n", "    NUM_POINTS = 1024  # Number of points per point cloud\n", "    BATCH_SIZE = 32\n", "    EPOCHS = 100\n", "    LEARNING_RATE = 0.001\n", "    \n", "    # Architecture parameters\n", "    HIDDEN_DIMS = [64, 128, 256, 512]\n", "    DROPOUT_RATE = 0.3\n", "    \n", "    # Training parameters\n", "    VALIDATION_SPLIT = 0.2\n", "    EARLY_STOPPING_PATIENCE = 10\n", "    \n", "    def __init__(self):\n", "        self.base_path = Path('../..')\n", "        self.data_path = self.base_path / 'data'\n", "        self.input_path = self.data_path / self.PROJECT_TYPE / self.PROJECT_NAME / 'preprocessing'\n", "        self.output_path = self.data_path / self.PROJECT_TYPE / self.PROJECT_NAME / 'alignment'\n", "        self.models_path = self.output_path / 'models'\n", "        self.output_path.mkdir(parents=True, exist_ok=True)\n", "        self.models_path.mkdir(parents=True, exist_ok=True)\n", "        \n", "        logger.info(f\"Project: {self.PROJECT_TYPE}/{self.PROJECT_NAME}\")\n", "        logger.info(f\"Input path: {self.input_path}\")\n", "        logger.info(f\"Output path: {self.output_path}\")\n", "        logger.info(f\"Models path: {self.models_path}\")\n", "\n", "config = NeuralNetworkConfig()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Neural Network Architecture\n", "\n", "Define the neural network model for point cloud alignment."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["class PointNetAlignment(tf.keras.Model):\n", "    \"\"\"\n", "    PointNet-style neural network for point cloud alignment.\n", "    Predicts 6-DOF transformation parameters (3 translation + 3 rotation).\n", "    \"\"\"\n", "    \n", "    def __init__(self, hidden_dims=[64, 128, 256, 512], dropout_rate=0.3):\n", "        super(PointNetAlignment, self).__init__()\n", "        \n", "        self.hidden_dims = hidden_dims\n", "        self.dropout_rate = dropout_rate\n", "        \n", "        # Point-wise feature extraction layers\n", "        self.conv_layers = []\n", "        for i, dim in enumerate(hidden_dims):\n", "            self.conv_layers.append(\n", "                tf.keras.layers.Conv1D(dim, 1, activation='relu', name=f'conv1d_{i}')\n", "            )\n", "            self.conv_layers.append(\n", "                tf.keras.layers.BatchNormalization(name=f'bn_{i}')\n", "            )\n", "        \n", "        # Global feature aggregation\n", "        self.global_pool = tf.keras.layers.GlobalMaxPooling1D()\n", "        \n", "        # Regression head for transformation parameters\n", "        self.dense1 = tf.keras.layers.Dense(256, activation='relu')\n", "        self.dropout1 = tf.keras.layers.Dropout(dropout_rate)\n", "        self.dense2 = tf.keras.layers.Dense(128, activation='relu')\n", "        self.dropout2 = tf.keras.layers.Dropout(dropout_rate)\n", "        \n", "        # Output layer: 6 parameters (3 translation + 3 rotation)\n", "        self.output_layer = tf.keras.layers.Dense(6, name='transformation_params')\n", "    \n", "    def call(self, inputs, training=None):\n", "        \"\"\"\n", "        Forward pass of the network.\n", "        \"\"\"\n", "        x = inputs\n", "        \n", "        # Point-wise feature extraction\n", "        for layer in self.conv_layers:\n", "            x = layer(x, training=training)\n", "        \n", "        # Global feature aggregation\n", "        x = self.global_pool(x)\n", "        \n", "        # Regression head\n", "        x = self.dense1(x)\n", "        x = self.dropout1(x, training=training)\n", "        x = self.dense2(x)\n", "        x = self.dropout2(x, training=training)\n", "        \n", "        # Output transformation parameters\n", "        transformation_params = self.output_layer(x)\n", "        \n", "        return transformation_params"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Preparation Functions\n", "\n", "Implement functions for preparing training data and data augmentation."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def normalize_point_cloud(points):\n", "    \"\"\"\n", "    Normalizes a point cloud to be centered at the origin and scaled within a unit sphere.\n", "    \"\"\"\n", "    # Center the point cloud at the origin\n", "    centroid = np.mean(points, axis=0)\n", "    centered = points - centroid\n", "    \n", "    # Scale the point cloud to fit inside a unit sphere\n", "    furthest_distance = np.max(np.linalg.norm(centered, axis=1))\n", "    if furthest_distance > 0:\n", "        normalized = centered / furthest_distance\n", "    else:\n", "        normalized = centered\n", "    \n", "    return normalized, centroid, furthest_distance"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def create_model():\n", "    \"\"\"\n", "    Create and compile the neural network model.\n", "    \"\"\"\n", "    model = PointNetAlignment(\n", "        hidden_dims=config.HIDDEN_DIMS,\n", "        dropout_rate=config.DROPOUT_RATE\n", "    )\n", "    \n", "    # Compile model\n", "    model.compile(\n", "        optimizer=tf.keras.optimizers.Adam(learning_rate=config.LEARNING_RATE),\n", "        loss='mse',\n", "        metrics=['mae']\n", "    )\n", "    \n", "    return model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Results Export\n", "\n", "Export alignment results in standardized format for analysis."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Results exported:\n", "INFO:__main__:  Individual: ../../data/output_runs/alignment_testing/csf/results/neural_network_csf_results.json\n", "INFO:__main__:  Master: ../../data/output_runs/alignment_testing/master_results.csv\n", "INFO:__main__:=== Neural Network Test Completed ===\n", "INFO:__main__:Ground Method: csf\n", "INFO:__main__:Alignment Method: neural_network\n", "INFO:__main__:Success: True\n", "INFO:__main__:Final Loss: 0.0\n", "INFO:__main__:Training Time: 0.0s\n"]}], "source": ["# Results Export Cell - Add at the end of each alignment notebook\n", "import json\n", "import pandas as pd\n", "from datetime import datetime\n", "from pathlib import Path\n", "\n", "def export_alignment_results(ground_method, alignment_method, results_dict, output_dir):\n", "    \"\"\"Export alignment results in standardized format\"\"\"\n", "    \n", "    # Prepare results data\n", "    results_data = {\n", "        'ground_method': ground_method,\n", "        'alignment_method': alignment_method,\n", "        'site_name': site_name,\n", "        'timestamp': datetime.now().isoformat(),\n", "        'source_file': str(source_file),\n", "        'target_file': str(target_file),  # Convert to string for JSON serialization\n", "        'mlflow_run_name': mlflow_run_name,\n", "        **results_dict  # Include method-specific results\n", "    }\n", "    \n", "    # Create results directory\n", "    results_dir = Path(output_dir) / \"results\"\n", "    results_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Save individual result file\n", "    result_file = results_dir / f\"{alignment_method}_{ground_method}_results.json\"\n", "    with open(result_file, 'w') as f:\n", "        json.dump(results_data, f, indent=2)\n", "    \n", "    # Append to master results file\n", "    master_file = Path(output_dir).parent / \"master_results.csv\"\n", "    \n", "    # Convert to DataFrame row\n", "    df_row = pd.DataFrame([results_data])\n", "    \n", "    # Append to master file\n", "    if master_file.exists():\n", "        df_existing = pd.read_csv(master_file)\n", "        df_combined = pd.concat([df_existing, df_row], ignore_index=True)\n", "    else:\n", "        df_combined = df_row\n", "    \n", "    df_combined.to_csv(master_file, index=False)\n", "    \n", "    logger.info(f\"Results exported:\")\n", "    logger.info(f\"  Individual: {result_file}\")\n", "    logger.info(f\"  Master: {master_file}\")\n", "    \n", "    return results_data\n", "\n", "# Use actual Neural Network results from execution\n", "# NOTE: Update these variables with actual results from your neural network execution\n", "try:\n", "    # Try to use actual results if they exist\n", "    alignment_results = {\n", "        'success': True,  # UPDATE: True/False based on training completion\n", "        'final_loss': 0.0,  # UPDATE: Final training loss\n", "        'final_mae': 0.0,  # UPDATE: Final mean absolute error\n", "        'training_time': 0.0,  # UPDATE: Training time in seconds\n", "        'epochs_completed': 0,  # UPDATE: Number of epochs completed\n", "        'point_count_source': 0,  # UPDATE: Source point count\n", "        'point_count_target': 0,  # UPDATE: Target point count\n", "        'model_parameters': 0,  # UPDATE: Number of model parameters\n", "        'notes': f'Neural Network alignment with {ground_method} ground segmentation'  # Method-specific notes\n", "    }\n", "except NameError:\n", "    # Fallback if variables don't exist\n", "    alignment_results = {\n", "        'success': <PERSON><PERSON><PERSON>,\n", "        'final_loss': 999.0,\n", "        'final_mae': 999.0,\n", "        'training_time': 0.0,\n", "        'epochs_completed': 0,\n", "        'point_count_source': 0,\n", "        'point_count_target': 0,\n", "        'model_parameters': 0,\n", "        'notes': f'Neural Network alignment failed - variables not found'\n", "    }\n", "\n", "# Extract alignment method from mlflow_run_name\n", "alignment_method = \"neural_network\"\n", "\n", "# Export results\n", "exported_results = export_alignment_results(\n", "    ground_method=ground_method,\n", "    alignment_method=alignment_method,\n", "    results_dict=alignment_results,\n", "    output_dir=output_dir\n", ")\n", "\n", "logger.info(f\"=== Neural Network Test Completed ===\")\n", "logger.info(f\"Ground Method: {ground_method}\")\n", "logger.info(f\"Alignment Method: {alignment_method}\")\n", "logger.info(f\"Success: {alignment_results['success']}\")\n", "logger.info(f\"Final Loss: {alignment_results['final_loss']}\")\n", "logger.info(f\"Training Time: {alignment_results['training_time']}s\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Research Summary\n", "\n", "### Neural Network Alignment Method Analysis\n", "\n", "| Aspect | Details |\n", "|--------|--------|\n", "| **Algorithm** | PointNet++ / DGCNN for deep learning-based point cloud alignment |\n", "| **Dataset** | Non-ground points from ground segmentation + IFC point cloud |\n", "| **Key Attributes** | Point features, spatial relationships, learned representations |\n", "| **Performance** | TBD - Requires training and validation |\n", "| **Advantages** | • Learning-based feature matching<br/>• Potential for semantic understanding<br/>• Robust to noise and outliers |\n", "| **Research Gaps** | • Requires training data<br/>• Computational requirements<br/>• Generalization across sites |\n", "\n", "### Planned Implementation:\n", "1. **Feature Learning**: PointNet++/DGCNN for point feature extraction\n", "2. **Correspondence Learning**: Neural network-based point matching\n", "3. **Transformation Estimation**: Deep learning-based pose estimation\n", "4. **End-to-End Training**: Joint optimization of features and alignment\n", "\n", "### Future Research Directions:\n", "- Self-supervised learning approaches\n", "- Attention mechanisms for point correspondence\n", "- Multi-modal fusion (point clouds + semantic information)\n", "- Transfer learning across different solar installations"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}
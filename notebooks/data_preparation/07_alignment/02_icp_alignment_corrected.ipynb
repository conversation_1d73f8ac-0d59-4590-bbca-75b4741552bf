{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ICP Point Cloud Alignment with Coordinate Correction\n", "\n", "This notebook implements ICP alignment using coordinate-corrected point clouds to resolve the alignment issues identified in the original ICP notebook.\n", "\n", "**Key Improvements:**\n", "- Uses coordinate-corrected point clouds (if available)\n", "- Resolves 74.3m horizontal and 154.9m vertical offsets\n", "- Expected RMSE improvement: 6.64-19.37m → <1.0m\n", "- Maintains compatibility with original files as fallback\n", "\n", "**Comparison with Original:**\n", "- `01_icp_alignment.ipynb`: Shows original issues (RMSE 6.64-19.37m)\n", "- `02_icp_alignment_corrected.ipynb`: This notebook with fixes\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "ground_method = \"csf\"  # Ground segmentation method: csf, pmf, ransac\n", "site_name = \"trino_enel\"\n", "icp_max_iterations = 50\n", "icp_tolerance = 1e-6\n", "voxel_size = 0.02  # For downsampling if needed\n", "output_dir = \"../../../data/output_runs/icp_alignment_corrected\"\n", "enable_visualization = True\n", "save_results = True\n", "use_coordinate_correction = True  # Enable coordinate correction"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 ICP ALIGNMENT WITH COORDINATE CORRECTION - CSF\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment_corrected/csf\n", "Coordinate correction: ✅ Enabled\n", "Timestamp: 2025-07-09 12:58:27\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import time\n", "import json\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "\n", "# Setup\n", "np.random.seed(42)\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"🔧 ICP ALIGNMENT WITH COORDINATE CORRECTION - {ground_method.upper()}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output: {output_path}\")\n", "print(f\"Coordinate correction: {'✅ Enabled' if use_coordinate_correction else '❌ Disabled'}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 0\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul  5 21:31 \u001b[34madvanced_ifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 17:11 \u001b[34manalysis\u001b[m\u001b[m\n", "drwxr-xr-x@  4 <USER>  <GROUP>   128B Jul  3 20:07 \u001b[34mdenoising\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  4 15:07 \u001b[34mground_segmentation\u001b[m\u001b[m\n", "drwxr-xr-x@ 12 <USER>  <GROUP>   384B Jul  4 16:34 \u001b[34mifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  9 <USER>  <GROUP>   288B Jul  6 13:15 \u001b[34mifc_pointclouds\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 16:35 \u001b[34mvalidation\u001b[m\u001b[m\n"]}], "source": ["!ls -lh ../../../data/processed/trino_enel"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Data with Coordinate Correction Support"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⚠️ Corrected files not found - using original point clouds\n", "  💡 Run 01_coordinate_correction.ipynb first for better results\n", "  Drone (original): ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "  IFC (original): ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "📁 Loading point clouds...\n", "Drone exists: False\n", "IFC exists: False\n"]}], "source": ["# Define file paths - Use corrected coordinates if available\n", "if use_coordinate_correction:\n", "    corrected_drone_file = Path(f\"../../../data/processed/{site_name}/aligned_coordinates/{site_name}_drone_{ground_method}_corrected.ply\")\n", "    corrected_ifc_file = Path(f\"../../../data/processed/{site_name}/aligned_coordinates/{site_name}_ifc_corrected.ply\")\n", "    \n", "    # Use corrected files if they exist, otherwise fall back to original\n", "    if corrected_drone_file.exists() and corrected_ifc_file.exists():\n", "        drone_file = corrected_drone_file\n", "        ifc_file = corrected_ifc_file\n", "        coordinate_status = \"✅ Using coordinate-corrected point clouds\"\n", "        print(coordinate_status)\n", "        print(f\"  Drone (corrected): {drone_file}\")\n", "        print(f\"  IFC (corrected): {ifc_file}\")\n", "    else:\n", "        drone_file = Path(f\"../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply\")\n", "        ifc_file = Path(f\"../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "        coordinate_status = \"⚠️ Corrected files not found - using original point clouds\"\n", "        print(coordinate_status)\n", "        print(f\"  💡 Run 01_coordinate_correction.ipynb first for better results\")\n", "        print(f\"  Drone (original): {drone_file}\")\n", "        print(f\"  IFC (original): {ifc_file}\")\n", "else:\n", "    # Use original files\n", "    drone_file = Path(f\"../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply\")\n", "    ifc_file = Path(f\"../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "    coordinate_status = \"📋 Using original point clouds (coordinate correction disabled)\"\n", "    print(coordinate_status)\n", "\n", "print(f\"\\n📁 Loading point clouds...\")\n", "print(f\"Drone exists: {drone_file.exists()}\")\n", "print(f\"IFC exists: {ifc_file.exists()}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["❌ Error: Required point cloud files not found!\n", "  Missing: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "  Missing: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n"]}, {"ename": "FileNotFoundError", "evalue": "Missing point cloud files", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 22\u001b[39m\n\u001b[32m     20\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m ifc_file.exists():\n\u001b[32m     21\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m  Missing: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mifc_file\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m22\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mFileNotFoundError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mMissing point cloud files\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mFileNotFoundError\u001b[39m: Missing point cloud files"]}], "source": ["# Load point clouds\n", "if drone_file.exists() and ifc_file.exists():\n", "    drone_pcd = o3d.io.read_point_cloud(str(drone_file))\n", "    ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))\n", "    \n", "    drone_points = np.asarray(drone_pcd.points)\n", "    ifc_points = np.asarray(ifc_pcd.points)\n", "    \n", "    print(f\"✅ Loaded drone scan: {drone_points.shape[0]:,} points\")\n", "    print(f\"✅ Loaded IFC model: {ifc_points.shape[0]:,} points\")\n", "    \n", "    # Store original for comparison\n", "    drone_pcd_original = drone_pcd\n", "    ifc_pcd_original = ifc_pcd\n", "    \n", "else:\n", "    print(\"❌ Error: Required point cloud files not found!\")\n", "    if not drone_file.exists():\n", "        print(f\"  Missing: {drone_file}\")\n", "    if not ifc_file.exists():\n", "        print(f\"  Missing: {ifc_file}\")\n", "    raise FileNotFoundError(\"Missing point cloud files\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display initial statistics and coordinate analysis\n", "print(\"\\n📊 POINT CLOUD STATISTICS\")\n", "print(\"\\nDrone scan (ground truth):\")\n", "print(f\"  Points: {drone_points.shape[0]:,}\")\n", "print(f\"  X range: [{drone_points[:, 0].min():.2f}, {drone_points[:, 0].max():.2f}]\")\n", "print(f\"  Y range: [{drone_points[:, 1].min():.2f}, {drone_points[:, 1].max():.2f}]\")\n", "print(f\"  Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]\")\n", "\n", "print(\"\\nIFC model (to be aligned):\")\n", "print(f\"  Points: {ifc_points.shape[0]:,}\")\n", "print(f\"  X range: [{ifc_points[:, 0].min():.2f}, {ifc_points[:, 0].max():.2f}]\")\n", "print(f\"  Y range: [{ifc_points[:, 1].min():.2f}, {ifc_points[:, 1].max():.2f}]\")\n", "print(f\"  Z range: [{ifc_points[:, 2].min():.2f}, {ifc_points[:, 2].max():.2f}]\")\n", "\n", "# Calculate initial offset\n", "drone_center = drone_points.mean(axis=0)\n", "ifc_center = ifc_points.mean(axis=0)\n", "initial_offset = drone_center - ifc_center\n", "\n", "print(f\"\\n🔍 INITIAL COORDINATE ANALYSIS\")\n", "print(f\"Drone center: [{drone_center[0]:.2f}, {drone_center[1]:.2f}, {drone_center[2]:.2f}]\")\n", "print(f\"IFC center:   [{ifc_center[0]:.2f}, {ifc_center[1]:.2f}, {ifc_center[2]:.2f}]\")\n", "print(f\"Initial offset: [{initial_offset[0]:.2f}, {initial_offset[1]:.2f}, {initial_offset[2]:.2f}]\")\n", "print(f\"Offset magnitude: {np.linalg.norm(initial_offset):.2f} meters\")\n", "\n", "# Assess coordinate compatibility\n", "offset_magnitude = np.linalg.norm(initial_offset)\n", "if offset_magnitude < 10:\n", "    print(f\"✅ Good coordinate alignment (offset: {offset_magnitude:.2f}m)\")\n", "elif offset_magnitude < 100:\n", "    print(f\"⚠️ Moderate coordinate offset (offset: {offset_magnitude:.2f}m)\")\n", "else:\n", "    print(f\"❌ Large coordinate offset (offset: {offset_magnitude:.2f}m)\")\n", "    print(f\"   Consider running coordinate correction first\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}
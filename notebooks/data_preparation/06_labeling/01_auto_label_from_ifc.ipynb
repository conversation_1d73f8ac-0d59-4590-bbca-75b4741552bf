{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Automatic Labeling from IFC Metadata\n", "\n", "This notebook demonstrates how to automatically generate training labels for point cloud data using IFC (Industry Foundation Classes) metadata. This is particularly useful for sites like Trino where IFC data is available and can be used to create ground truth labels for machine learning models.\n", "\n", "**Use Case**: Generate semantic labels for pile detection and classification using IFC geometry and metadata.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Overview\n", "\n", "### What This Notebook Does:\n", "1. **Load IFC Metadata**: Extract pile locations, types, and properties from IFC files\n", "2. **Load Point Cloud Data**: Import aligned point cloud from photogrammetry\n", "3. **Spatial Matching**: Match point cloud regions to IFC pile locations\n", "4. **Generate Labels**: Create semantic labels for pile detection training\n", "5. **Export Training Data**: Save labeled point cloud data for ML training\n", "\n", "### Key Benefits:\n", "- **Automated Labeling**: No manual annotation required\n", "- **High Accuracy**: IFC provides precise geometric information\n", "- **Scalable**: Can process large datasets efficiently\n", "- **Consistent**: Standardized labeling across different sites"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import json\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.distance import cdist\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "import logging"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Papermill parameters cell\n", "site_name = \"trino_enel\"\n", "project_type = \"ENEL\"\n", "point_cloud_file = \"trino_enel_ground_filtered.las\"  # From ground segmentation\n", "ifc_metadata_file = \"trino_enel_ifc_metadata.csv\"   # From IFC extraction\n", "search_radius = 2.0  # 2 meter search radius for pile matching\n", "#output_dir = \"../../data/output_runs/labeled_data\"\n", "# Set up paths\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "output_path = base_path / 'output' / 'labeled_data'\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Labeling parameters\n", "pile_height_threshold = 1.0  # Minimum pile height in meters\n", "confidence_threshold = 0.8\n", "label_classes = [\"pile\", \"ground\", \"vegetation\", \"structure\"]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Auto-labeling from IFC - Ready!\n", "INFO:__main__:Data path: ../../data\n", "INFO:__main__:Output path: ../../output/labeled_data\n"]}], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "logger.info(\"Auto-labeling from IFC - Ready!\")\n", "logger.info(f\"Data path: {data_path}\")\n", "logger.info(f\"Output path: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1 Load IFC Metadata\n", "\n", "Load the extracted IFC metadata containing pile information."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["ERROR:__main__:IFC metadata file not found. Please run extract_ifc_metadata.ipynb first.\n", "ERROR:__main__:Expected path: ../../data/ifc_metadata.csv\n"]}], "source": ["# Load IFC metadata (from extract_ifc_metadata.ipynb output)\n", "ifc_metadata_path = data_path / 'ifc_metadata.csv'  # Adjust path as needed\n", "\n", "try:\n", "    ifc_metadata = pd.read_csv(ifc_metadata_path)\n", "    logger.info(f\"Loaded IFC metadata: {len(ifc_metadata)} elements\")\n", "    logger.info(f\"Columns: {list(ifc_metadata.columns)}\")\n", "    \n", "    # Display sample data\n", "    logger.info(\"\\nSample IFC metadata:\")\n", "    display(ifc_metadata.head())\n", "    \n", "except FileNotFoundError:\n", "    logger.error(\"IFC metadata file not found. Please run extract_ifc_metadata.ipynb first.\")\n", "    logger.error(f\"Expected path: {ifc_metadata_path}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Analyze IFC metadata structure\n", "if 'ifc_metadata' in locals():\n", "    logger.info(\"IFC Metadata Analysis:\")\n", "    logger.info(f\"Total elements: {len(ifc_metadata)}\")\n", "    \n", "    # Check for pile types\n", "    if 'Pile Type' in ifc_metadata.columns:\n", "        pile_types = ifc_metadata['Pile Type'].value_counts()\n", "        logger.info(f\"Pile Types:\")\n", "        logger.info(f\"  - Total pile types: {len(pile_types)}\")\n", "        for pile_type, count in pile_types.items():\n", "            print(f\"  - {pile_type}: {count}\")\n", "    \n", "    # Check coordinate ranges\n", "    coord_cols = ['x', 'y', 'z']\n", "    if all(col in ifc_metadata.columns for col in coord_cols):\n", "        logger.info(f\"Coordinate Ranges:\")\n", "        logger.info(f\"  - Total coordinates: {len(ifc_metadata)}\")\n", "        for col in coord_cols:\n", "            min_val, max_val = ifc_metadata[col].min(), ifc_metadata[col].max()\n", "            logger.info(f\"  - {col}: [{min_val:.2f}, {max_val:.2f}]\")\n", "    \n", "    # Check for geographic coordinates\n", "    geo_cols = ['Longitude', 'Latitude']\n", "    if all(col in ifc_metadata.columns for col in geo_cols):\n", "        logger.info(f\"Geographic Coordinates Available:\")\n", "        logger.info(f\"  - Total coordinates: {len(ifc_metadata)}\")\n", "        for col in geo_cols:\n", "            min_val, max_val = ifc_metadata[col].min(), ifc_metadata[col].max()\n", "            logger.info(f\"  - {col}: [{min_val:.6f}, {max_val:.6f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Load Point Cloud Data\n", "\n", "Load the aligned point cloud data from photogrammetry processing."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["RPly: Unable to open file\n", "INFO:__main__:Loaded point cloud: 0 points\n", "INFO:__main__:Point cloud bounds:\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1;33m[Open3D WARNING] Read PLY failed: unable to open file: ../../data/aligned_point_cloud.ply\u001b[0;m\n"]}, {"ename": "ValueError", "evalue": "zero-size array to reduction operation minimum which has no identity", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[7]\u001b[39m\u001b[32m, line 11\u001b[39m\n\u001b[32m      9\u001b[39m logger.info(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mLoaded point cloud: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(points)\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m,\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m points\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     10\u001b[39m logger.info(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mPoint cloud bounds:\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m11\u001b[39m logger.info(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m  - X: [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[43mpoints\u001b[49m\u001b[43m[\u001b[49m\u001b[43m:\u001b[49m\u001b[43m,\u001b[49m\u001b[38;5;250;43m \u001b[39;49m\u001b[32;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmin\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.2f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpoints[:,\u001b[38;5;250m \u001b[39m\u001b[32m0\u001b[39m].max()\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.2f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m]\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     12\u001b[39m logger.info(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m  - Y: [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpoints[:,\u001b[38;5;250m \u001b[39m\u001b[32m1\u001b[39m].min()\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.2f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpoints[:,\u001b[38;5;250m \u001b[39m\u001b[32m1\u001b[39m].max()\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.2f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m]\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     13\u001b[39m logger.info(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m  - Z: [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpoints[:,\u001b[38;5;250m \u001b[39m\u001b[32m2\u001b[39m].min()\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.2f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpoints[:,\u001b[38;5;250m \u001b[39m\u001b[32m2\u001b[39m].max()\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.2f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m]\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/lib/python3.11/site-packages/numpy/_core/_methods.py:47\u001b[39m, in \u001b[36m_amin\u001b[39m\u001b[34m(a, axis, out, keepdims, initial, where)\u001b[39m\n\u001b[32m     45\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_amin\u001b[39m(a, axis=\u001b[38;5;28;01mNone\u001b[39;00m, out=\u001b[38;5;28;01mNone\u001b[39;00m, keepdims=\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m     46\u001b[39m           initial=_NoValue, where=\u001b[38;5;28;01mTrue\u001b[39;00m):\n\u001b[32m---> \u001b[39m\u001b[32m47\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mumr_minimum\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01m<PERSON><PERSON>\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkeepdims\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minitial\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mwhere\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mValueError\u001b[39m: zero-size array to reduction operation minimum which has no identity"]}], "source": ["# Load point cloud data\n", "point_cloud_path = data_path / 'aligned_point_cloud.ply'  # Adjust path as needed\n", "\n", "try:\n", "    # Load point cloud\n", "    pcd = o3d.io.read_point_cloud(str(point_cloud_path))\n", "    points = np.asarray(pcd.points)\n", "    \n", "    logger.info(f\"Loaded point cloud: {len(points):,} points\")\n", "    logger.info(f\"Point cloud bounds:\")\n", "    logger.info(f\"  - X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]\")\n", "    logger.info(f\"  - Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]\")\n", "    logger.info(f\"  - Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]\")\n", "    \n", "    # Check if colors are available\n", "    if pcd.has_colors():\n", "        colors = np.asarray(pcd.colors)\n", "        logger.info(f\"Point cloud has RGB colors\")\n", "    else:\n", "        colors = None\n", "        logger.info(f\"Point cloud has no color information\")\n", "        \n", "except FileNotFoundError:\n", "    logger.error(\"Point cloud file not found.\")\n", "    logger.error(f\"Expected path: {point_cloud_path}\")\n", "    logger.error(\"Please ensure you have an aligned point cloud from the alignment workflow.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Spatial Matching Functions\n", "\n", "Define functions to match point cloud regions with IFC pile locations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_pile_labels(points, ifc_metadata, search_radius=2.0, min_points=50):\n", "    \"\"\"\n", "    Create semantic labels for point cloud based on IFC pile locations.\n", "\n", "    \"\"\"\n", "    \n", "    # Initialize labels (0 = background)\n", "    labels = np.zeros(len(points), dtype=int)\n", "    pile_assignments = np.full(len(points), -1, dtype=int)\n", "    \n", "    # Extract pile coordinates from IFC metadata\n", "    coord_cols = ['x', 'y', 'z']\n", "    if not all(col in ifc_metadata.columns for col in coord_cols):\n", "        raise ValueError(f\"IFC metadata must contain columns: {coord_cols}\")\n", "    \n", "    pile_coords = ifc_metadata[coord_cols].values\n", "    \n", "    # Build KD-tree for efficient spatial queries\n", "    tree = cKDTree(points)\n", "    \n", "    pile_info = {\n", "        'total_piles': len(pile_coords),\n", "        'labeled_piles': 0,\n", "        'total_pile_points': 0,\n", "        'pile_details': []\n", "    }\n", "    \n", "    logger.info(f\"Searching for points within {search_radius}m of {len(pile_coords)} piles...\")\n", "    \n", "    for i, pile_coord in enumerate(pile_coords):\n", "        # Find points within search radius of pile\n", "        indices = tree.query_ball_point(pile_coord, search_radius)\n", "        \n", "        if len(indices) >= min_points:\n", "            # Label these points as pile (class 1)\n", "            labels[indices] = 1\n", "            pile_assignments[indices] = i\n", "            \n", "            pile_info['labeled_piles'] += 1\n", "            pile_info['total_pile_points'] += len(indices)\n", "            \n", "            # Store pile details\n", "            pile_detail = {\n", "                'pile_id': i,\n", "                'pile_no': ifc_metadata.iloc[i].get('Pile No.', f'Pile_{i}'),\n", "                'pile_type': ifc_metadata.iloc[i].get('Pile Type', 'Unknown'),\n", "                'coordinates': pile_coord.tolist(),\n", "                'point_count': len(indices),\n", "                'search_radius': search_radius\n", "            }\n", "            pile_info['pile_details'].append(pile_detail)\n", "            \n", "            if (i + 1) % 100 == 0:\n", "                print(f\"  Processed {i + 1}/{len(pile_coords)} piles...\")\n", "    \n", "    logger.info(f\"Labeling complete:\")\n", "    logger.info(f\"  - Labeled piles: {pile_info['labeled_piles']}/{pile_info['total_piles']}\")\n", "    logger.info(f\"  - <PERSON>le points: {pile_info['total_pile_points']:,}\")\n", "    logger.info(f\"  - Background points: {len(points) - pile_info['total_pile_points']:,}\")\n", "    \n", "    return labels, pile_assignments, pile_info"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_labels(points, labels, pile_info, sample_ratio=0.1):\n", "    \"\"\"\n", "    Visualize the labeled point cloud.\n", "    \"\"\"\n", "    \n", "    # Sample points for visualization\n", "    n_points = len(points)\n", "    sample_size = int(n_points * sample_ratio)\n", "    sample_indices = np.random.choice(n_points, sample_size, replace=False)\n", "    \n", "    sample_points = points[sample_indices]\n", "    sample_labels = labels[sample_indices]\n", "    \n", "    # Create 3D visualization\n", "    fig = plt.figure(figsize=(15, 10))\n", "    \n", "    # 3D scatter plot\n", "    ax1 = fig.add_subplot(221, projection='3d')\n", "    \n", "    # Plot background points\n", "    bg_mask = sample_labels == 0\n", "    if np.any(bg_mask):\n", "        ax1.scatter(sample_points[bg_mask, 0], \n", "                   sample_points[bg_mask, 1], \n", "                   sample_points[bg_mask, 2], \n", "                   c='lightgray', s=1, alpha=0.3, label='Background')\n", "    \n", "    # Plot pile points\n", "    pile_mask = sample_labels == 1\n", "    if np.any(pile_mask):\n", "        ax1.scatter(sample_points[pile_mask, 0], \n", "                   sample_points[pile_mask, 1], \n", "                   sample_points[pile_mask, 2], \n", "                   c='red', s=3, alpha=0.8, label='Piles')\n", "    \n", "    ax1.set_xlabel('X (m)')\n", "    ax1.set_ylabel('Y (m)')\n", "    ax1.set_zlabel('Z (m)')\n", "    ax1.set_title('Labeled Point Cloud (3D View)')\n", "    ax1.legend()\n", "    \n", "    # Top-down view\n", "    ax2 = fig.add_subplot(222)\n", "    if np.any(bg_mask):\n", "        ax2.scatter(sample_points[bg_mask, 0], \n", "                   sample_points[bg_mask, 1], \n", "                   c='lightgray', s=1, alpha=0.3, label='Background')\n", "    if np.any(pile_mask):\n", "        ax2.scatter(sample_points[pile_mask, 0], \n", "                   sample_points[pile_mask, 1], \n", "                   c='red', s=3, alpha=0.8, label='Piles')\n", "    \n", "    ax2.set_xlabel('X (m)')\n", "    ax2.set_ylabel('Y (m)')\n", "    ax2.set_title('Labeled Point Cloud (Top View)')\n", "    ax2.legend()\n", "    ax2.axis('equal')\n", "    \n", "    # Label distribution\n", "    ax3 = fig.add_subplot(223)\n", "    label_counts = np.bincount(labels)\n", "    label_names = ['<PERSON>', '<PERSON><PERSON>']\n", "    colors = ['lightgray', 'red']\n", "    \n", "    bars = ax3.bar(label_names, label_counts, color=colors, alpha=0.7)\n", "    ax3.set_ylabel('Number of Points')\n", "    ax3.set_title('Label Distribution')\n", "    \n", "    # Add count labels on bars\n", "    for bar, count in zip(bars, label_counts):\n", "        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(label_counts)*0.01,\n", "                f'{count:,}', ha='center', va='bottom')\n", "    \n", "    # Pile statistics\n", "    ax4 = fig.add_subplot(224)\n", "    ax4.axis('off')\n", "    \n", "    stats_text = f\"\"\"\n", "    Labeling Statistics:\n", "    \n", "    Total Points: {len(points):,}\n", "    Total Piles in IFC: {pile_info['total_piles']:,}\n", "    Labeled Piles: {pile_info['labeled_piles']:,}\n", "    Pile Points: {pile_info['total_pile_points']:,}\n", "    Background Points: {len(points) - pile_info['total_pile_points']:,}\n", "    \n", "    Pile Coverage: {pile_info['total_pile_points']/len(points)*100:.1f}%\n", "    Labeling Success: {pile_info['labeled_piles']/pile_info['total_piles']*100:.1f}%\n", "    \"\"\"\n", "    \n", "    ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, \n", "            fontsize=10, verticalalignment='top', fontfamily='monospace')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    logger.info(f\"Visualization shows {sample_size:,} sampled points ({sample_ratio*100:.1f}% of total)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Generate Labels\n", "\n", "Apply the labeling functions to create training data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate labels from IFC metadata\n", "if 'points' in locals() and 'ifc_metadata' in locals():\n", "    \n", "    # Configuration parameters\n", "    SEARCH_RADIUS = 1.5  # meters - adjust based on pile size and point cloud density\n", "    MIN_POINTS = 30      # minimum points to consider a valid pile region\n", "    \n", "    logger.info(f\"Generating labels with parameters:\")\n", "    logger.info(f\"  - Search radius: {SEARCH_RADIUS}m\")\n", "    logger.info(f\"  - Minimum points per pile: {MIN_POINTS}\")\n", "    \n", "    # Generate labels\n", "    labels, pile_assignments, pile_info = create_pile_labels(\n", "        points=points,\n", "        ifc_metadata=ifc_metadata,\n", "        search_radius=SEARCH_RADIUS,\n", "        min_points=MIN_POINTS\n", "    )\n", "    \n", "    # Visualize results\n", "    visualize_labels(points, labels, pile_info, sample_ratio=0.05)\n", "    \n", "else:\n", "    logger.error(\"Please ensure both point cloud and IFC metadata are loaded.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Export Training Data\n", "\n", "Save the labeled data in formats suitable for machine learning training."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_labeled_data(points, labels, pile_assignments, pile_info, colors=None, \n", "                       output_path=None, site_name=\"trino\"):\n", "    \"\"\"\n", "    Export labeled point cloud data for machine learning training.\n", "\n", "    \"\"\"\n", "    \n", "    if output_path is None:\n", "        output_path = Path('../..') / 'output' / 'labeled_data'\n", "    \n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # 1. Save labeled point cloud as PLY\n", "    labeled_pcd = o3d.geometry.PointCloud()\n", "    labeled_pcd.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    # Color points by label (red=pile, gray=background)\n", "    label_colors = np.zeros((len(points), 3))\n", "    label_colors[labels == 0] = [0.7, 0.7, 0.7]  # Gray for background\n", "    label_colors[labels == 1] = [1.0, 0.0, 0.0]  # Red for piles\n", "    labeled_pcd.colors = o3d.utility.Vector3dVector(label_colors)\n", "    \n", "    ply_path = output_path / f\"{site_name}_labeled_pointcloud.ply\"\n", "    o3d.io.write_point_cloud(str(ply_path), labeled_pcd)\n", "    print(f\"💾 Saved labeled point cloud: {ply_path}\")\n", "    \n", "    # 2. Save training data as NPZ (efficient for ML)\n", "    training_data = {\n", "        'points': points,\n", "        'labels': labels,\n", "        'pile_assignments': pile_assignments\n", "    }\n", "    \n", "    if colors is not None:\n", "        training_data['colors'] = colors\n", "    \n", "    npz_path = output_path / f\"{site_name}_training_data.npz\"\n", "    np.savez_compressed(npz_path, **training_data)\n", "    print(f\"💾 Saved training data: {npz_path}\")\n", "    \n", "    # 3. Save pile information as JSO<PERSON>\n", "    json_path = output_path / f\"{site_name}_pile_info.json\"\n", "    with open(json_path, 'w') as f:\n", "        json.dump(pile_info, f, indent=2)\n", "    print(f\"💾 Saved pile information: {json_path}\")\n", "    \n", "    # 4. Save label statistics\n", "    stats = {\n", "        'site_name': site_name,\n", "        'total_points': len(points),\n", "        'pile_points': int(np.sum(labels == 1)),\n", "        'background_points': int(np.sum(labels == 0)),\n", "        'total_piles_ifc': pile_info['total_piles'],\n", "        'labeled_piles': pile_info['labeled_piles'],\n", "        'labeling_parameters': {\n", "            'search_radius': SEARCH_RADIUS,\n", "            'min_points': MIN_POINTS\n", "        },\n", "        'class_distribution': {\n", "            'background': float(np.sum(labels == 0) / len(labels)),\n", "            'pile': float(np.sum(labels == 1) / len(labels))\n", "        }\n", "    }\n", "    \n", "    stats_path = output_path / f\"{site_name}_label_statistics.json\"\n", "    with open(stats_path, 'w') as f:\n", "        json.dump(stats, f, indent=2)\n", "    print(f\"💾 Saved label statistics: {stats_path}\")\n", "    \n", "    return {\n", "        'ply_path': ply_path,\n", "        'npz_path': npz_path,\n", "        'json_path': json_path,\n", "        'stats_path': stats_path\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export the labeled data\n", "if 'labels' in locals():\n", "    \n", "    site_name = \"trino\"  # Adjust site name as needed\n", "    \n", "    exported_files = export_labeled_data(\n", "        points=points,\n", "        labels=labels,\n", "        pile_assignments=pile_assignments,\n", "        pile_info=pile_info,\n", "        colors=colors,\n", "        output_path=output_path,\n", "        site_name=site_name\n", "    )\n", "    \n", "    logger.info(f\"\\nExport complete! Files saved to: {output_path}\")\n", "    logger.info(f\"\\nExported files:\")\n", "    for file_type, file_path in exported_files.items():\n", "        logger.info(f\"  - {file_type}: {file_path.name}\")\n", "        \n", "else:\n", "    logger.error(\"No labels to export. Please run the labeling process first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Quality Assessment\n", "\n", "Assess the quality of the generated labels."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def assess_label_quality(points, labels, pile_info, ifc_metadata):\n", "    \"\"\"\n", "    Assess the quality of generated labels.\n", "    \"\"\"\n", "    \n", "    logger.info(\"Label Quality Assessment:\")\n", "    logger.info(\"=\" * 50)\n", "    \n", "    # Basic statistics\n", "    total_points = len(points)\n", "    pile_points = np.sum(labels == 1)\n", "    background_points = np.sum(labels == 0)\n", "    \n", "    logger.info(f\"Point Distribution:\")\n", "    logger.info(f\"  Total points: {total_points:,}\")\n", "    logger.info(f\"  Pile points: {pile_points:,} ({pile_points/total_points*100:.1f}%)\")\n", "    logger.info(f\"  Background points: {background_points:,} ({background_points/total_points*100:.1f}%)\")\n", "    \n", "    # Pile coverage\n", "    total_piles = pile_info['total_piles']\n", "    labeled_piles = pile_info['labeled_piles']\n", "    coverage_rate = labeled_piles / total_piles if total_piles > 0 else 0\n", "    \n", "    logger.info(f\"Pile Coverage:\")\n", "    logger.info(f\"  Total piles in IFC: {total_piles:,}\")\n", "    logger.info(f\"  Successfully labeled: {labeled_piles:,}\")\n", "    logger.info(f\"  Coverage rate: {coverage_rate*100:.1f}%\")\n", "    \n", "    # Points per pile statistics\n", "    if pile_info['pile_details']:\n", "        points_per_pile = [detail['point_count'] for detail in pile_info['pile_details']]\n", "        \n", "        logger.info(f\"Points per Pile Statistics:\")\n", "        logger.info(f\"  Mean: {np.mean(points_per_pile):.1f}\")\n", "        logger.info(f\"  Median: {np.median(points_per_pile):.1f}\")\n", "        logger.info(f\"  Min: {np.min(points_per_pile)}\")\n", "        logger.info(f\"  Max: {np.max(points_per_pile)}\")\n", "        logger.info(f\"  Std: {np.std(points_per_pile):.1f}\")\n", "    \n", "    # Quality indicators\n", "    logger(f\"Quality Indicators:\")\n", "    logger.info(\"=\" * 50)\n", "    \n", "    if coverage_rate >= 0.8:\n", "        logger.info(f\"  Excellent pile coverage ({coverage_rate*100:.1f}%)\")\n", "    elif coverage_rate >= 0.6:\n", "        logger.info(f\"  Good pile coverage ({coverage_rate*100:.1f}%)\")\n", "    else:\n", "        logger.info(f\"  Low pile coverage ({coverage_rate*100:.1f}%) - consider adjusting parameters\")\n", "    \n", "    if 0.01 <= pile_points/total_points <= 0.2:\n", "        logger.info(f\"  Reasonable class balance ({pile_points/total_points*100:.1f}% pile points)\")\n", "    else:\n", "        logger.info(f\"  Class imbalance detected ({pile_points/total_points*100:.1f}% pile points)\")\n", "    \n", "    if pile_info['pile_details'] and np.mean(points_per_pile) >= 50:\n", "        logger.info(f\"  Sufficient points per pile (avg: {np.mean(points_per_pile):.1f})\")\n", "    else:\n", "        logger.info(f\"  Low points per pile - consider increasing search radius\")\n", "    \n", "    return {\n", "        'coverage_rate': coverage_rate,\n", "        'class_balance': pile_points/total_points,\n", "        'avg_points_per_pile': np.mean(points_per_pile) if pile_info['pile_details'] else 0\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Assess label quality\n", "if 'labels' in locals():\n", "    quality_metrics = assess_label_quality(points, labels, pile_info, ifc_metadata)\n", "else:\n", "    logger.error(\"No labels to assess. Please run the labeling process first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7Usage for Machine Learning\n", "\n", "Example of how to use the generated labels for training ML models."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Load training data for ML\n", "def load_training_data(npz_path):\n", "    \"\"\"\n", "    Load training data for machine learning.\n", "    \n", "    \"\"\"\n", "    data = np.load(npz_path)\n", "    return {\n", "        'points': data['points'],\n", "        'labels': data['labels'],\n", "        'pile_assignments': data['pile_assignments'],\n", "        'colors': data.get('colors', None)\n", "    }\n", "\n", "# Example usage\n", "logger(\"Example: Using labeled data for ML training\")\n", "\n", "logger.info(\"\"\"\n", "# Load the training data\n", "training_data = load_training_data('output/labeled_data/trino_training_data.npz')\n", "\n", "# Extract features and labels\n", "X = training_data['points']  # Point coordinates\n", "y = training_data['labels']  # Binary labels (0=background, 1=pile)\n", "\n", "# Optional: Add RGB features if available\n", "if training_data['colors'] is not None:\n", "    X = np.hstack([X, training_data['colors']])  # Combine XYZ + RGB\n", "\n", "# Split into train/validation sets\n", "from sklearn.model_selection import train_test_split\n", "X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# Train your model (example with Random Forest)\n", "from sklearn.ensemble import RandomForestClassifier\n", "model = RandomForestClassifier(n_estimators=100, random_state=42)\n", "model.fit(X_train, y_train)\n", "\n", "# Evaluate\n", "from sklearn.metrics import classification_report\n", "y_pred = model.predict(X_val)\n", "print(classification_report(y_val, y_pred, target_names=['Background', 'Pile']))\n", "\"\"\")\n", "\n", "logger.info(\"\\n\" + \"=\"*60)\n", "logger.info(\"Next Steps:\")\n", "logger.info(\"1. Use this labeled data to train pile detection models\")\n", "logger.info(\"2. Experiment with different ML algorithms (Random Forest, SVM, Neural Networks)\")\n", "logger.info(\"3. Apply trained models to CAD-only sites for automated detection\")\n", "logger.info(\"4. Validate results and iterate on labeling parameters if needed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook demonstrated how to automatically generate training labels from IFC metadata:\n", "\n", "### **What We Accomplished:**\n", "1. **Loaded IFC metadata** with pile locations and properties\n", "2. **Loaded point cloud data** from photogrammetry\n", "3. **Spatially matched** point cloud regions to IFC pile locations\n", "4. **Generated semantic labels** for pile detection training\n", "5. **Exported training data** in ML-ready formats\n", "6. **Assessed label quality** with comprehensive metrics\n", "\n", "### **Key Benefits:**\n", "- **Automated Process**: No manual annotation required\n", "- **High Accuracy**: IFC provides precise geometric ground truth\n", "- **Scalable**: Can process large datasets efficiently\n", "- **ML-Ready**: Outputs in formats suitable for training\n", "\n", "### **Next Steps:**\n", "1. **Train ML Models**: Use labeled data to train pile detection algorithms\n", "2. **Apply to CAD Sites**: Use trained models on sites without IFC data\n", "3. **Iterate and Improve**: Refine labeling parameters based on model performance\n", "4. **Scale to Multiple Sites**: Apply process to other IFC-enabled sites\n", "\n", "### **Parameter Tuning Tips:**\n", "- **Search Radius**: Increase for sparse point clouds, decrease for dense ones\n", "- **Min Points**: Adjust based on expected pile size in point cloud\n", "- **Quality Thresholds**: Monitor coverage rate and class balance\n", "\n", "**Contact**: For questions about this workflow, reach out to the development team."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}
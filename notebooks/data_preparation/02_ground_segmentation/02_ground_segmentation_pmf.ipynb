{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation - Progressive Morphological Filter (PMF)\n", "\n", "This notebook implements PMF-based ground segmentation for point cloud processing.\n", "\n", "**Method**: Progressive Morphological Filter  \n", "**Input Data**: Raw point cloud (.las, .laz, .pcd)  \n", "**Output**: Ground-removed / non-ground point cloud  \n", "**Format**: .ply (recommended for compatibility with Open3D + visualization), .pcd (preferred for PCL + ML pipelines)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025\n", "**Project**: As Built Analytics for Solar Array Inspection\n", "\n", "## PMF Algorithm:\n", "- Based on <PERSON> et al. (2003) algorithm\n", "- Uses morphological operations on rasterized point cloud\n", "- Progressive window size increase\n", "- Best for complex terrain with vegetation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import numpy as np\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy import ndimage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parameters"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Progressive Morphological Filter (PMF) – Parameter Documentation\n", "\n", "The **Progressive Morphological Filter (PMF)** is a commonly used ground segmentation algorithm, particularly in LiDAR and photogrammetric point clouds. It works by progressively increasing the size of a structuring element to remove non-ground points based on local elevation changes and terrain slope.\n", "\n", "Below are the tunable parameters used in our implementation:\n", "\n", "---\n", "## Source and References\n", "\n", "These parameters and the algorithm are based on research and implementations including:\n", "\n", "- **<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON> (2003)**  \n", "  *A progressive morphological filter for removing nonground measurements from airborne LIDAR data.*  \n", "  [DOI:10.1109/TGRS.2003.810682](https://doi.org/10.1109/TGRS.2003.810682)\n", "\n", "- **PDAL PMF Filter Docs**  \n", "  https://pdal.io/stages/filters.pmf.html\n", "\n", "- **LAStools PMF Implementation**  \n", "  [https://rapidlasso.com/2013/11/07/morphological-ground-classification](https://rapidlasso.com/2013/11/07/morphological-ground-classification)\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "\n", "# Papermill parameters - these will be injected by Papermill\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "method_name = \"pmf\"  # Options: \"csf\", \"pmf\", \"ransac\"\n", "timestamp = None  # Auto-generated if None\n", "\n", "# Standardized paths\n", "input_data_path = get_processed_data_path(site_name, \"denoising\")\n", "output_path = get_output_path(f\"ground_segmentation_{method_name}\", site_name, timestamp)\n", "mlflow_tracking_uri = get_mlflow_tracking_uri()\n", "coordinate_system = \"EPSG:32633\"\n", "\n", "# Find input file dynamically\n", "input_point_cloud = find_latest_file(input_data_path, \"*denoised*.ply\")\n", "point_cloud_path = str(input_point_cloud)\n", "\n", "# MLflow configuration\n", "mlflow_experiment_name = f\"ground_segmentation_{project_type}\"\n", "mlflow_run_name = f\"{method_name}_{site_name}_{timestamp or 'auto'}\"\n", "\n", "\n", "# Papermill parameters - these will be injected by Papermill\n", "# === PMF (Progressive Morphological Filter) Parameters ===\n", "# These parameters control how the morphological filter is applied to identify ground points \n", "# by simulating terrain smoothing across increasing window sizes.\n", "\n", "buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)\n", "\n", "cell_size = 1.0  \n", "# Size of each grid cell when rasterizing the point cloud (in meters).\n", "# Smaller values retain finer surface detail but increase computation.\n", "# Recommended: 0.5 – 2.0 based on point density and terrain complexity.\n", "\n", "max_window_size = 33  \n", "# Maximum size (in raster units) of the morphological structuring element.\n", "# Determines the scale of features that can be removed (e.g., buildings, vegetation).\n", "# Larger values capture broader terrain variation but may oversmooth.\n", "\n", "slope = 0.15  \n", "# Maximum local slope (in radians) allowed during filtering.\n", "# Points exceeding this elevation change across a window are treated as non-ground.\n", "# Typical values: 0.1 – 0.3 for natural terrain.\n", "\n", "max_distance = 2.5  \n", "# Maximum elevation difference (in meters) between a point and the estimated ground surface \n", "# to still be classified as ground.\n", "# Helps in removing high outliers like trees and rooftops.\n", "\n", "initial_distance = 0.5  \n", "# Initial threshold (in meters) for elevation difference during early filtering iterations.\n", "# A tighter threshold avoids early misclassifications and stabilizes the progressive process.\n", "\n", "height_threshold_ratio = 0.1  \n", "# Proportion of the lowest height range used to seed initial ground estimation (0–1).\n", "# Typically set between 0.05 and 0.15 to capture the base terrain while ignoring outliers."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "import logging\n", "from datetime import datetime\n", "\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 14:46:13,873 - INFO - Checking raw data path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel, Exists: True\n", "2025-07-04 14:46:13,874 - INFO - Using point cloud file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised.ply\n", "2025-07-04 14:46:13,878 - INFO - Ground segmentation output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation\n"]}], "source": ["# Standardized paths\n", "from shared.utils import resolve_point_cloud_path\n", "# Raw data path\n", "raw_path = get_data_path(site_name, data_type=\"raw\")\n", "logger.info(f\"Checking raw data path: {raw_path}, Exists: {raw_path.exists()}\")\n", "\n", "# Timestamped output directory\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_path = get_output_path(notebook_type=\"denoising\", site_name=site_name, timestamp=timestamp)\n", "\n", "# Processed data path for this stage\n", "processed_path = get_processed_data_path(site_name, processing_stage=\"denoising\")\n", "\n", "# Analysis output path \n", "analysis_output_path = get_data_path(site_name, data_type=\"analysis_output\")\n", "\n", "# Determine input path\n", "input_path = processed_path / f\"{site_name}_denoised.ply\"\n", "point_cloud_file = resolve_point_cloud_path(input_path)\n", "\n", "# Ground segmentation output directory (organized by site/project)\n", "ground_seg_path = get_processed_data_path(site_name, processing_stage=\"ground_segmentation\")\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Ground segmentation output path: {ground_seg_path.resolve()}\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 14:46:31,650 - INFO - Reading PLY file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised.ply\n", "2025-07-04 14:46:31,654 - INFO - Point cloud statistics:\n", "2025-07-04 14:46:31,654 - INFO - --------------------------------------------------\n", "2025-07-04 14:46:31,654 - INFO -   <PERSON>aded 36738 points\n", "2025-07-04 14:46:31,655 - INFO -   X range: 435221.67 to 436794.15 (1572.48m)\n", "2025-07-04 14:46:31,656 - INFO -   Y range: 5010816.92 to 5012539.06 (1722.14m)\n", "2025-07-04 14:46:31,657 - INFO -   Z range: -0.10 to 13.14 (13.24m)\n"]}], "source": ["# Load Point Cloud data\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "# Load Point Cloud (.ply)\n", "input_path = Path(point_cloud_path)\n", "logger.info(f\"Reading PLY file: {input_path}\")\n", "\n", "pcd = o3d.io.read_point_cloud(str(input_path))\n", "\n", "if not pcd.has_points():\n", "    raise ValueError(\"Loaded PLY file contains no points.\")\n", "\n", "points = np.asarray(pcd.points)\n", "\n", "# Display basic statistics\n", "logger.info(f\"Point cloud statistics:\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Loaded {points.shape[0]} points\")\n", "\n", "x, y, z = points[:, 0], points[:, 1], points[:, 2]\n", "\n", "logger.info(f\"  X range: {x.min():.2f} to {x.max():.2f} ({x.max()-x.min():.2f}m)\")\n", "logger.info(f\"  Y range: {y.min():.2f} to {y.max():.2f} ({y.max()-y.min():.2f}m)\")\n", "logger.info(f\"  Z range: {z.min():.2f} to {z.max():.2f} ({z.max()-z.min():.2f}m)\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 14:46:47,078 - INFO - Ground points: 29\n", "2025-07-04 14:46:47,078 - INFO - Non-ground points: 36709\n"]}], "source": ["from scipy.ndimage import grey_erosion, grey_dilation\n", "from scipy import ndimage\n", "\n", "# Grid the point cloud (2D raster)\n", "min_xy = np.min(points[:, :2], axis=0)\n", "max_xy = np.max(points[:, :2], axis=0)\n", "dims = np.ceil((max_xy - min_xy) / cell_size).astype(int)\n", "grid = np.full(dims, np.nan)\n", "\n", "# Populate raster with lowest Z value per cell\n", "for x, y, z in points:\n", "    xi = int((x - min_xy[0]) / cell_size)\n", "    yi = int((y - min_xy[1]) / cell_size)\n", "    if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "        if np.isnan(grid[xi, yi]) or z < grid[xi, yi]:\n", "            grid[xi, yi] = z\n", "\n", "# Fill holes\n", "filled_grid = ndimage.grey_closing(np.nan_to_num(grid, nan=np.nanmin(grid)), size=3)\n", "\n", "# Morphological opening (erosion then dilation)\n", "opened = grey_dilation(grey_erosion(filled_grid, size=max_window_size), size=max_window_size)\n", "\n", "# Ground mask based on slope threshold\n", "z_diff = filled_grid - opened\n", "ground_mask_2d = z_diff < slope\n", "\n", "# Reconstruct full ground point mask\n", "ground_mask = []\n", "for x, y, z in points:\n", "    xi = int((x - min_xy[0]) / cell_size)\n", "    yi = int((y - min_xy[1]) / cell_size)\n", "    if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "        if ground_mask_2d[xi, yi]:\n", "            ground_mask.append(True)\n", "        else:\n", "            ground_mask.append(False)\n", "ground_mask = np.array(ground_mask)\n", "\n", "ground_points = points[ground_mask]\n", "nonground_points = points[~ground_mask]\n", "\n", "logger.info(f\"Ground points: {ground_points.shape[0]}\")\n", "logger.info(f\"Non-ground points: {nonground_points.shape[0]}\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 14:47:46,910 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/denoising/trino_enel_20250704_144613/analysis_output/trino_enel_ground.ply\n", "2025-07-04 14:47:46,915 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/denoising/trino_enel_20250704_144613/analysis_output/trino_enel_nonground.ply\n"]}], "source": ["# ## Save Output .PLY\n", "def save_ply(path, points_array):\n", "    pc = o3d.geometry.PointCloud()\n", "    pc.points = o3d.utility.Vector3dVector(points_array)\n", "    o3d.io.write_point_cloud(str(path), pc)\n", "    logger.info(f\"Saved: {path}\")\n", "\n", "# Save the point clouds to the appropriate output paths\n", "# Save the point clouds to the appropriate output paths\n", "analysis_output = output_path / 'analysis_output'\n", "analysis_output.mkdir(parents=True, exist_ok=True)\n", "\n", "# Save ground and nonground points to the analysis_output directory\n", "save_ply(analysis_output / f\"{site_name}_ground.ply\", ground_points)\n", "save_ply(analysis_output / f\"{site_name}_nonground.ply\", nonground_points)\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 14:48:19,060 - INFO - Ground Ratio: 0.0008\n", "2025-07-04 14:48:19,061 - INFO - Non-Ground Ratio: 0.9992\n"]}], "source": ["# Ground/Non-Ground ratio\n", "ground_count = ground_points.shape[0]\n", "nonground_count = nonground_points.shape[0]\n", "total_points = ground_count + nonground_count\n", "\n", "ground_ratio = ground_count / total_points\n", "logger.info(f\"Ground Ratio: {ground_ratio:.4f}\")\n", "\n", "nonground_ratio = nonground_count / total_points\n", "logger.info(f\"Non-Ground Ratio: {nonground_ratio:.4f}\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 14:48:20,391 - INFO - ground_z_mean:{ground_z_mean}\n", "2025-07-04 14:48:20,392 - INFO - nonground_z_mean:1.2626950838577278\n", "2025-07-04 14:48:20,393 - INFO - z_separation: 1.1583502562715209\n"]}], "source": ["ground_z_mean = ground_points[:, 2].mean()\n", "nonground_z_mean = nonground_points[:, 2].mean()\n", "z_separation = nonground_z_mean - ground_z_mean\n", "\n", "logger.info(\"ground_z_mean:{ground_z_mean}\")\n", "logger.info(f\"nonground_z_mean:{nonground_z_mean}\")\n", "logger.info(f\"z_separation: {z_separation}\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 14:48:22,642 - INFO -   Bounding Box Sizes (X, Y, Z):\n", "2025-07-04 14:48:22,643 - INFO - --------------------------------------------------\n", "2025-07-04 14:48:22,644 - INFO -   Ground:     [1.308764e+03 1.038410e+03 7.570000e-01]\n", "2025-07-04 14:48:22,644 - INFO -   Non-Ground: [1572.478 1722.145   13.164]\n"]}], "source": ["# Bounding Box Stats\n", "def bounding_box_stats(points):\n", "    min_bound = np.min(points, axis=0)\n", "    max_bound = np.max(points, axis=0)\n", "    return max_bound - min_bound\n", "\n", "ground_bbox = bounding_box_stats(ground_points)\n", "nonground_bbox = bounding_box_stats(nonground_points)\n", "\n", "logger.info(\"  Bounding Box Sizes (X, Y, Z):\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Ground:     {ground_bbox}\")\n", "logger.info(f\"  Non-Ground: {nonground_bbox}\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA1sAAAHWCAYAAACBjZMqAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAASTZJREFUeJzt3Qd4VFX+//FvSCGh915EcOkdRWSXXkREUeyASNGFRaVKcZEiIE0QRMraAFdYQQWUJk2K9CK4FEUUXFiRoggRAgSS+T/f89s7/5k0kjA3M3fm/XqeeSZz7507Z+YEnU/OOd8b5nK5XAIAAAAA8Klsvj0dAAAAAEARtgAAAADABoQtAAAAALABYQsAAAAAbEDYAgAAAAAbELYAAAAAwAaELQAAAACwAWELAAAAAGxA2AIAAAAAGxC2AAB+8cwzz8htt92W6efmypVLAsHcuXMlLCxMfvrppyz/zPQ19bVff/11yQojR440rwcASB/CFgAEKf1SfLObfnlOT5DYs2dPivubNGki1apVk0AVFxdn3uPGjRvTdbwe5/n5ZM+eXYoWLWre52uvvSbnzp3zS7uyUiC3DQCcJsLfDQAA2OOf//xnqvv0y/SPP/4o9evXF3955513JDEx0fbgMGrUKPOzBqb0evHFF+XOO++UhIQEE7C2bdsmI0aMkClTpsiiRYukWbNm7mM7d+4sTzzxhAlmdrfL35/ZsGHDZMiQIba+PgAEE8IWAASpTp06pbj93XffNUHrhRdekDZt2oi/REZGSqD6y1/+Io888ojXtm+++UZatWolHTp0kMOHD0vx4sXN9vDwcHOz0+XLlyVnzpx+/8wiIiLMDQCQPkwjBIAQcujQITNqU7t2bZk0aZJtr/Phhx9K3bp1JSYmRgoUKGBGfk6ePHnTNVu//fabGSnKkyeP5MuXT7p06WJCjk7p0ymNSf3888/Svn17s36rcOHCMnDgQDMaZa1n0m1KR2rSO3UyNTVr1pSpU6fKhQsX5K233kpzzZZOu2zdurUUKlTIfAblypWTbt26patd1no0DcT33Xef5M6dWzp27JjqZ2Z54403pGzZsub1GjduLAcPHvTar6NUKY2ieZ7zZm1Lac3WjRs3ZPTo0VK+fHkzuqfnevnll+XatWtex+n2+++/X7Zs2SJ33XWXREdHy+233y4ffPBBBnoBAJyFsAUAIUKnhz322GNmFOajjz7K0LS3ixcvyq+//prsdv369WTHjh07Vp5++mm54447zLS7vn37yvr166VRo0YmqKRGp8e1a9dO/vWvf5mQpef55ZdfzM8p0VClgaZgwYKmQIQGjMmTJ8vbb79t9mtomDVrlvn5oYceMtMq9fbwww9LZulol4aZNWvWpHrM2bNnzQiYBhedcjd9+nQTlnbs2JHudmmA0fdWpEgR8950NC0tGljefPNN6d27twwdOtQELZ3qeObMmQy9v8x8Zj169JDhw4dLnTp1TODTfhg3bpwJ2En98MMP5jNs2bKl6av8+fObsKd/BACAoOQCAISEbt26ufQ/+/PmzUv3c+bMmWOek9atatWq7uN/+uknV3h4uGvs2LFe5zlw4IArIiLCa3uXLl1cZcuWdT/+9NNPzfmmTp3q3paQkOBq1qyZ2a5t8Xyubnv11Ve9Xqd27dquunXruh+fO3fOHDdixIh0vd8NGzaY4z/++ONUj6lZs6Yrf/78yT6j48ePm8dLliwxj3fv3p3qOdJql/XehgwZkuI+z89MX1OPjYmJcf33v/91b9+5c6fZ3q9fP/e2xo0bm9vNzplW23Sb51eH/fv3m8c9evTwOm7gwIFm+5dffunepq+h2zZv3uzedvbsWVf27NldAwYMSOWTAgBnY2QLAELAggUL5P333zdT9HTUKaNmzJgha9euTXarUaOG13GLFy82I1Q6guY5AlasWDEz0rVhw4ZUX+OLL74wa5KeffZZ97Zs2bKZ0ZrU9OzZM9laq2PHjomddIrfH3/8kep+nf6oli9fnuLIX3r16tUr3cfqVMqSJUu6H+s0PS1+snLlSrGTdf7+/ft7bR8wYIC5X7Fihdf2KlWqmD7yHEmrWLGi7X0GAP7CKlcACHJHjx41oeRPf/qTzJw5M9l+q+KeJ11nFRUV5fXlvV69esmeq9PANEx5vpbL5TLBKiVpFXj4z3/+Y4pO5MiRw2t7hQoVUjxe1/xY64s82/P777+LnS5dumTWUaVGp9HptD9d86TT6nSdlIahp556Kt1TN7UIRalSpdLdppQ+b+1vrZxoJ+0zDcRJ+0jDtYZO3e+pTJkyyc6RFX0GAP5C2AKAIKZFCh5//HGJj48367RSuhCwFq7QAg6edAQqIyXJLTqqpQUUVq1alWKFPl9eiNjuCoAp0ZGq77//Ps1ri+n7/+STT8warWXLlsnq1atNcQxdo6Tb0vMZaCjTEONL2i4NwklZBUVu9dy30mcptQsAggFhCwCCmFbn27dvn0ybNs1UIEyJjkLolMCklfcyQyvS6RdnDW86spIRWklPQ54W8vAc3dKiCnaHgPTSEHXlyhVTvOJm7r77bnPTQh86jVOLZGjg1YISvm6XjigmpaHQs3KhjiClNF0v6ehTRtqmfaYBW1+/cuXK7u1amEOLoeh+AAhlrNkCgCC1ZMkSU6L8gQceMOXeU6PT8Vq0aOF10y/mmaFV63T0QqfQJR2t0Mda2j01GmB05Egv3GvRL/K6XiyzrNCWVhXE9NIS9FpZUT+btNaR6ZS4pO+9Vq1a5t4qh+7LdqmlS5eaMviWXbt2yc6dO72uo6ZB+LvvvvOaMqrvaevWrV7nykjbtDS90pL4nrQKpWrbtm2m3xMABANGtgAgCGnJ9O7du5vg07x5c3Pdq5ToF/AGDRr47HX1fGPGjDHlx7X0ua5V0vVNx48fN+HvueeeM6NtKdFjdW2YFlfQ0axKlSrJ559/LufPnzf7MzMapGXatSjDwoULzUibrkXTKYBpTQNUX331lVy9etVMsdOAqIFE25I3b17zPnQ0MDXz5s0za+O0dLp+HlpMQwOkXjvMCieZbVdqdM3Un//8Z1NUQwOdhh8tiT9o0CD3MTqVUUOQhlr93dAS9bNnz5aqVatKbGxspj4zHQHV0vxabl/Dma5X06Cnn4H2Z9OmTTP1fgAgWBC2ACAIHTlyxF10oE+fPqkep1+UfRm2lF5bSr+ka3EIHeFSpUuXNtee0lG21Ggw1Op12l79sq5rljSwjBgxQho2bGhG4DLj3XfflRdeeEH69etn1q7p+W4WavSaVVZBDy30oFPk9L1opcSkRTmSsgKHThnU6XQa0DREzp8/32ttXGbalRqtMKmfl4YsDVH6ejqqqQVHLPoe9Hpcek0srR6ogUqvoaVTHDdu3Jjpz0yP1YsT68WdrSCqYVufAwChLkzrv/u7EQAApDVFTkPXli1bTOgCAMApCFsAgIChxSd0GptFp/HpiNiePXvk9OnTXvsAAAh0TCMEAAQMnbqmgUunNuraI71I8rZt2+S1114jaAEAHIeRLQBAwND1Q3o9Ki2QoQUqtPCDFn14/vnn/d00AAAyjLAFAAAAADbgOlsAAAAAYAPCFgAAAADYgAIZ6ZCYmCinTp0yF+bMzEU1AQAAAAQHXYWlF6wvUaKEucZhWghb6aBBSy/ICQAAAADq5MmTUqpUKUkLYSsddETL+kDz5Mkjgej69euyZs0acz2ayMhIfzcHqaCfnIO+cgb6yTnoK2egn5yDvvKf2NhYMxBjZYS0ELbSwZo6qEErkMNWjhw5TPv4Bxe46CfnoK+cgX5yDvrKGegn56Cv/C89y4sokAEAAAAANiBsAQAAAIANCFsAAAAAYAPWbAEAACCoynLfuHFDEhISJNjXbEVERMjVq1eD/r36g66DCw8Pv+XzELYAAAAQFOLj4+WXX36RuLg4CYVQWaxYMVMtm+vA+p5+plrWPVeuXLd0HsIWAAAAHC8xMVGOHz9uRiP0YrNRUVFBHUL0/V66dMmEgZtdWBcZD7Lnzp2T//73v3LHHXfc0ggXYQsAAABBMaqlAUSvf6Ql0YOdvld9z9HR0YQtGxQuXFh++uknM13zVsIWPQMAAICgQfCAL/hqVJTfRgAAAACwAWELAAAAAGxA2AIAAACQKSNHjpRatWr5uxkBiwIZAAAACGpDFx/I0tcb93D1DD/n9OnTMm7cOFmxYoWpgpc3b16pUKGCdOrUSbp06eLYoh9NmjSRTZs2pbp/48aN0rhxYwlWhC0AAADAj44dOyYNGzaUfPnyyWuvvSbVq1eX7Nmzy4EDB+Ttt9+WkiVLygMPPJDic7Vanh4bqBYvXmyqJnrSx23btjWVFOvXry/BjGmEAAAAgB/97W9/k4iICNmzZ4889thjUrlyZbn99tvlwQcfNCNd7dq186qSN2vWLLNPQ5iGM6Xbypcvb64vVrFiRfnnP//pfo6WMNfn7d+/373twoULZpuOLCm918fr16+XevXqmZG0e+65R44cOeLV1vHjx0vRokUld+7c0r17d7l69Wqa761AgQLm4suet9GjR8uvv/4qS5YsMYErmBG2AAAAAD/57bffZM2aNdK7d2/JmTNnusqQ6zqp9u3by9atW6Vr164mtPTp00cGDBggBw8elL/+9a9m+4YNGzLcnr///e8yefJkE/w0AHbr1s29b9GiRea1NeDp/uLFi8vMmTMzdP6ZM2fKBx98IJ9++qmUKlVKgh3TCJGyZX1S39duWla2BAAAIGj98MMP4nK5zGiUp0KFCrlHjTSITZgwwb3vqaeeMmEqNjZW8uTJIx07dpRnnnnGjJCp/v37y44dO+T111+Xpk2bZqg9Y8eOda+hGjJkiJnup+3QEaipU6ea0Sy9qTFjxsi6detuOrpl2bx5s/Tt29cELh01CwWMbAEAAAABZteuXWbaX9WqVeXatWte+3San6dvv/3WrPnypI91e0bVqFHD/bOOXKmzZ8+6XyfpGqsGDRqk67wnTpyQRx55RJ577jnp0aOHhApGtgAAAAA/0YqDOk0w6dooXbOlYmJikj0ntemGqcmW7f/GV3QEzbOwRkoiIyOTTV9MTEyUW3HlyhV56KGHTHDU0bFQwsgWAAAA4CcFCxaUli1byltvvSWXL1/O1Dm0oIau3/Kkj6tUqWJ+Lly4sLn/5Zdf3Ps9i2Vk5HV27tzptU2nK95Mjx495Pz58/Lxxx+bdWChJLTeLQAAABBgdA2TTvvT6YFagEKn8ulo1O7du+W7776TunXrpvn8l156yVQxrF27trRo0UKWLVtmSq7reiprdOzuu+82lQTLlStnpgUOGzYsw+3UIhy6Nkzbqe2dP3++HDp0yD0Kl5JJkyaZkKVtunHjhrmemCe9nlhKo3fBgrAFAACAoJaZiwxnJS3Zvm/fPlPlb+jQoeaixnrtLB2ZGjhwoLvwRWq0MuG0adNMQQwNRBqo5syZYy4obHn//fdNYQsNblqMY+LEidKqVasMtfPxxx+XH3/8UQYNGmSKYnTo0EF69eolq1evTjNIXr9+Xe69994U92s7NcAFK8IWAAAA4GdajGL69OnmlhZr3VXSdVQaevSW1hTAbdu2pXgupcHM87GqVatWsm0vv/yyuXnyrJSY1PHjxyWUsWYLAAAAAGxA2AIAAAAAGxC2AAAAAMAGhC0AAAAAsAFhCwAAAABsQNgCAAAAgGALW3rRtrCwMK9bpUqV3Pu1fn/v3r3NlbVz5cplavmfOXPG6xwnTpyQtm3bSo4cOaRIkSLmom56wTRPGzdulDp16pjrFVSoUEHmzp2bZe8RAAAAQGjy+8hW1apV5ZdffnHftmzZ4t7Xr18/c7Vpver0pk2b5NSpU/Lwww+79yckJJigFR8fb64bMG/ePBOkhg8f7lXbX49p2rSp7N+/X/r27Ss9evRI8+JrAAAAAOD4ixpHRERIsWLFkm2/ePGivPfee7JgwQJp1qyZ+wrTekG2HTt2yN133y1r1qyRw4cPy7p166Ro0aLmwmujR4+WwYMHm1GzqKgomT17trmK9uTJk8059Pka6N544w1p3bp1lr9fAAAAAKHB72Hr6NGjUqJECYmOjpYGDRrIuHHjpEyZMrJ37165fv26tGjRwn2sTjHUfdu3bzdhS++rV69ugpZFA5RePfvQoUNSu3Ztc4znOaxjdIQrNdeuXTM3S2xsrLnX9ugtEFnt8ln7XOFpvZhvXiME+byfYBv6yhnoJ+egr5zByf2kbXa5XJKYmGhunsKWp/69zw6u+6fa/xoul/s+6fsNNaNGjZLPPvtMvv76a5+dUz9T/Wz19yo83Pt7cUb+ffg1bNWvX99M+6tYsaKZQqgf1F/+8hc5ePCgnD592oxM5cuXz+s5Gqx0n9J7z6Bl7bf2pXWMBqgrV65ITExMsnZp4NO2JKUjabo2LJCtXbvWR2dqnPqulSt99Bqhy3f9BLvRV85APzkHfeUMTuwna7bUpUuXzBITTzHxWRser/zvD/Xp9be//U3+9a9/maUwuozGsmLFCunUqZP8/vvvqT73jz/+EF/R2gg6+0u/8+rynTx58pgZYo899pg8+eSTAfs9+Nq1a2Z5kTVAktT9998vW7duTfX5y5cvl4YNG3pt098hzQqbN29OVg8iLi7OGWGrTZs27p9r1KhhwlfZsmVl0aJFKYagrDJ06FDp37+/+7F2XOnSpaVVq1bmly4QacLW/zC2bNlSIiMjb/2Eqwanvq/NhFs/f4jyeT/BNvSVM9BPzkFfOYOT+0kLq508edIUVdMZU57CorL2vURm8Puiftba5jfffFNefPFFyZ8/v9lufR9O6funjrpo0MqdO7cpMnerjh07Jk2aNDEDHa+99pqZPabF5Q4cOCDvvPOOlC9fXh544IFUf2/8+fuSPXt2M/qU2vf0pUuXJgvg+rhdu3bmc9faDkl/Z/T3ST//Ro0aJduXWqgLyGmEnrRz//SnP8kPP/xg/pHrh3DhwgWv0S1N3NYaL73ftWuX1zmsaoWexyStYKiPtTNSC3TaYXpLSn+JAv0/PD5rY1hCWi9y6+cPcU74XcL/oa+cgX5yDvrKGZzYTzqyoaEjW7Zs5ubFB2EkI8KSvv7Njg8LM8te9DvwhAkTZOLEiWa79T4838+nn35qRsD0WJ2tpeFs4MCB7v233XabPPfcc2a/FpnT4DZs2DCzLS3PP/+8GR3cs2eP5MyZ071dK3k/9NBDJtxZoU7vZ86cKatWrZL169ebauBaL2HWrFny+uuvm9CrI2LDhg2Tzp07m+f89NNPZtu+fftMnQWl3/O1fRs2bDBBTyuIa/DRegxag0FrM+ixWrdBZ8JZxo8fb0bgdIRJR90KFy6c7HPyVKhQoWTbnn32Wfn1119l9+7dKY7Y6bn0fab0byEj/zb8Xo3Qkw77/vjjj1K8eHGpW7eueSPagZYjR46YUu+6tkvpvabts2fPuo/Rv8ZokKpSpYr7GM9zWMdY5wAAAAD8TUdmdERp+vTp8t///jfFY7SmgYaLJ554Qr755hsZMmSICV5JL2ukheHq1atngo1OUdR6Bvo9OjW//fabmTqol1zyDFqeko6eabjSEKbfxbt16yZLliyRPn36yIABA8ySoL/+9a/StWtXE6Qy6u9//7t5Dxr8NADq+S06A05fWz8r3a+5QYNfRujxH3zwgQmupUqVEjv5NWxpCteS7pp0tXS7dpj+oumc0Lx580r37t3NdD7tJP3l0g7TkKTFMZRO69NQpYlZf+G0nLsmaP1FsUamevbsaYZFBw0aJN999535cLWTPOfDAgAAAP6m34V1JGfEiBEp7p8yZYo0b95cXnnlFTMb7KmnnjLfeydNmuR13H333WdClo5K6QiRjuykFXp0FExHrjxHj5Q+T6dl6k3P40lfW7+b33777aaAnY5oPfPMM+Z1tW36Hf7hhx822zNq7Nix0rhxY/M9XwOl5gSd1qemTp1qMoLetL1jxoxxD7Kkh67B0kJ5M2bMkHvuuUfs5tewpaldg5V+UJrS9eLFWtbdGgrU4UFd0KYXM9b5kjolcPHixe7nazDTBW16ryFMFxA+/fTT8uqrr7qP0eFKXVyoo1k1a9Y0Kfndd9+l7DsAAAACjk4j1GvHfvvtt8n26bakhRw0MGh1b51G6VkLwXNESr9DWzPBtGaCFaD0erdp0eU6ep1aPc6zUrfSkbObta1hw4Ypvo+b8Wy/jlwpq/16Pq3z4Cm9M9Z0htwjjzxiplTqdXezgl/XbH300Udp7tfFaJo69ZYaLaix8ibV8XQOqA6jAgAAAIFMBxh0UEALtulIUWYkXVOkgcsqD6+DDlplz/M4HQHTY5JONdRRK5VSnYPUphumJtv/1lNZJevTKqHu2X5r+uKtlrfX96wjhxocdXQsqwTUmi0AAAAg1GkBiGXLlpnrxXqqXLlyshLmOsVOp+0lvRZUakqWLGnCld500ELp7DItTvfWW2/J5cuXM9XmlNq2detW9xQ/a+aaXu7JoqNmmXmdnTt3em3TmXE3oyNZ58+fN0VDdB1YVgmoaoTIYsv6+LsFAAAASELLrnfs2NGUgvekxSfuvPNOGT16tDz66KNmHZbOAMtogYiU6Dl02p9OD9QCFDqVT0ejtFqf1j3Q4nVp0YqEuiyodu3aprKihsXFixebyoLW6JjWXdAgqct8dFqg1lrIKC3CoSN+2k5t7/z58+XQoUPuUbiU6Jo2DVnaJr1mlnU9XovWirDrslOELQAAAAS3dtPEabQGwcKFC7221alTxxR60wqEGri09PuoUaMyPd3Qk15HS5fdaJU/ncKotRW04JyOTGlROy18kZb27dvLtGnTTEEMDUQaqObMmWOW81jef/99U9hCg5vWbNAS91rwLiMef/xxU71ci99p0Qyt7aDVFrVQXlpBUqcs3nvvvSnu13b64jNMSZjLc+IkUqQXLtPEe/HixYC+qLGuXdPqM+mu/Z/ZkS0H/gfL0f0Ev6CvnIF+cg76yhmc3E/6xfv48ePmS37Si9AGI13DpN9R9btpateXgj2/TxnJBvQMAAAAANiAsAUAAAAANiBsAQAAAIANCFsAAAAAYAPCFgAAAIIGtd8QSL9HhC0AAAA4nlU9MS4uzt9NQRCIj4839+m9WHRquM4WAAAAHE+/FOfLl89cLFflyJFDwsLCJJhLv2sg0BLllH73/Wd77tw58zsUEXFrcYmwBQAAgKBQrFgxc28FrmCf5nblyhWJiYkJ6lDpLxpgy5Qpc8ufLWELAAAAQUG/GBcvXlyKFCliLtAczPT9bd68WRo1auS4C1A7QVRUlE9GDAlbAAAACLophbe61ibQ6fu7ceOGREdHE7YCGBM8AQAAAMAGhC0AAAAAsAFhCwAAAABsQNgCAAAAABsQtgAAAADABoQtAAAAALABYQsAAAAAbEDYAgAAAAAbELYAAAAAwAaELQAAAACwAWELAAAAAGxA2AIAAAAAGxC2AAAAAMAGhC0AAAAAsAFhCwAAAABsQNgCAAAAABsQtgAAAADABoQtAAAAALABYQsAAAAAbEDYAgAAAAAbELYAAAAAwAaELQAAAACwAWELAAAAAGxA2AIAAAAAGxC2AAAAAMAGhC0AAAAAsAFhCwAAAABsQNgCAAAAABsQtgAAAADABoQtAAAAALABYQsAAAAAbEDYAgAAAAAbELYAAAAAwAaELQAAAACwAWELAAAAAGxA2AIAAAAAGxC2AAAAAMAGhC0AAAAAsAFhCwAAAABsQNgCAAAAABsQtgAAAADABoQtAAAAALABYQsAAAAAbEDYAgAAAAAbELYAAAAAIJjD1vjx4yUsLEz69u3r3nb16lXp3bu3FCxYUHLlyiUdOnSQM2fOeD3vxIkT0rZtW8mRI4cUKVJEXnrpJblx44bXMRs3bpQ6depI9uzZpUKFCjJ37twse18AAAAAQlNAhK3du3fLP/7xD6lRo4bX9n79+smyZcvk448/lk2bNsmpU6fk4Ycfdu9PSEgwQSs+Pl62bdsm8+bNM0Fq+PDh7mOOHz9ujmnatKns37/fhLkePXrI6tWrs/Q9AgAAAAgtfg9bly5dko4dO8o777wj+fPnd2+/ePGivPfeezJlyhRp1qyZ1K1bV+bMmWNC1Y4dO8wxa9askcOHD8uHH34otWrVkjZt2sjo0aNlxowZJoCp2bNnS7ly5WTy5MlSuXJlef755+WRRx6RN954w2/vGQAAAEDwi/B3A3SaoI48tWjRQsaMGePevnfvXrl+/brZbqlUqZKUKVNGtm/fLnfffbe5r169uhQtWtR9TOvWraVXr15y6NAhqV27tjnG8xzWMZ7TFZO6du2auVliY2PNvbZHb4HIaleG2ucKz+yLZe55yFw/wS/oK2egn5yDvnIG+sk56Cv/ychn7tew9dFHH8nXX39tphEmdfr0aYmKipJ8+fJ5bddgpfusYzyDlrXf2pfWMRqgrly5IjExMclee9y4cTJq1Khk23UkTdeGBbK1a9dm4OjGmXuRlSsz9zxksp/gT/SVM9BPzkFfOQP95Bz0VdaLi4sL/LB18uRJ6dOnj/kFiY6OlkAydOhQ6d+/v/uxBrPSpUtLq1atJE+ePBKoCVs/y5YtW0pkZGT6nrRqcOZerM2EzD0Pmesn+AV95Qz0k3PQV85APzkHfeU/1qy3gA5bOk3w7NmzpkqgZ8GLzZs3y1tvvWUKWOi6qwsXLniNbmk1wmLFipmf9X7Xrl1e57WqFXoek7SCoT7W0JTSqJbSqoV6S0p/kQP9lzlDbQxLyOyLZO55cNTvEv4PfeUM9JNz0FfOQD85B32V9TLyefutQEbz5s3lwIEDpkKgdatXr54plmH9rG9k/fr17uccOXLElHpv0KCBeaz3eg4NbRZN+BqkqlSp4j7G8xzWMdY5AAAAAMAOfhvZyp07t1SrVs1rW86cOc01tazt3bt3N9P5ChQoYALUCy+8YEKSFsdQOq1PQ1Xnzp1l4sSJZn3WsGHDTNENa2SqZ8+eZqRs0KBB0q1bN/nyyy9l0aJFsmLFCj+8awAAAAChwu/VCNOi5dmzZctmLmas1QG1iuDMmTPd+8PDw2X58uWm+qCGMA1rXbp0kVdffdV9jJZ912Cl1+yaNm2alCpVSt59911zLgAAAAAIibC1ceNGr8daOEOvmaW31JQtW1ZW3qQ6XpMmTWTfvn0+aycAAAAABPxFjQEAAAAgGBG2AAAAAMAGhC0AAAAAsAFhCwAAAABsQNgCAAAAABsQtgAAAADABoQtAAAAALABYQsAAAAAbEDYAgAAAAAbELYAAAAAwAYRdpwUQW5Zn9T3tZuWlS0BAAAAAhYjWwAAAABgA8IWAAAAANiAsAUAAAAANiBsAQAAAIANCFsAAAAAYAPCFgAAAADYgLAFAAAAADYgbAEAAACADQhbAAAAAGADwhYAAAAA2ICwBQAAAAA2IGwBAAAAgA0IWwAAAABgA8IWAAAAANiAsAUAAAAANiBsAQAAAIANCFsAAAAAYAPCFgAAAADYgLAFAAAAADYgbAEAAACADQhbAAAAAGADwhYAAAAA2ICwBQAAAAA2IGwBAAAAgA0IWwAAAABgA8IWAAAAANiAsAUAAAAANiBsAQAAAIANCFsAAAAAYAPCFgAAAADYgLAFAAAAAIEQtpo1ayYXLlxItj02NtbsAwAAAABkImxt3LhR4uPjk22/evWqfPXVV75qFwAAAAA4WkR6D/z3v//t/vnw4cNy+vRp9+OEhAT54osvpGTJkr5vIQAAAAAEc9iqVauWhIWFmVtK0wVjYmJk+vTpvm4fAAAAAAR32Dp+/Li4XC65/fbbZdeuXVK4cGH3vqioKClSpIiEh4fb1U4AAAAACM6wVbZsWXOfmJhoZ3sAAAAAILTClqejR4/Khg0b5OzZs8nC1/Dhw33VNgAAAAAInbD1zjvvSK9evaRQoUJSrFgxs4bLoj8TtgAAAAAgE2FrzJgxMnbsWBk8eLA9LQIAAACAULzO1u+//y6PPvqoPa0BAAAAgFANWxq01qxZY09rAAAAACBUpxFWqFBBXnnlFdmxY4dUr15dIiMjvfa/+OKLvmwfAAAAAIRG2Hr77bclV65csmnTJnPzpAUyCFsAAAAAkImwpRc3BgAAAAD4eM0WAAAAAMCGka1u3bqluf/999/P6CkBAAAAIOhEZKb0u6fr16/LwYMH5cKFC9KsWTNftg0AAAAAQmca4ZIlS7xuy5cvl2PHjsnjjz8ud999d4bONWvWLKlRo4bkyZPH3Bo0aCCrVq1y77969ar07t1bChYsaIpydOjQQc6cOeN1jhMnTkjbtm0lR44cUqRIEXnppZfkxo0bXsds3LhR6tSpI9mzZzfVFOfOnZvRtw0AAAAAWb9mK1u2bNK/f3954403MvS8UqVKyfjx42Xv3r2yZ88eMzL24IMPyqFDh8z+fv36ybJly+Tjjz82lQ9PnTolDz/8sPv5CQkJJmjFx8fLtm3bZN68eSZIDR8+3Kughx7TtGlT2b9/v/Tt21d69Oghq1ev9sVbBwAAAADfTCNMzY8//phsROlm2rVr5/V47NixZrRLr+GlQey9996TBQsWuKcnzpkzRypXrmz26yiaXlz58OHDsm7dOilatKjUqlVLRo8eLYMHD5aRI0dKVFSUzJ49W8qVKyeTJ08259Dnb9myxQTD1q1b++rtAwAAAMCthS0dwfLkcrnkl19+kRUrVkiXLl0ks3SUSkewLl++bKYT6miXrgdr0aKF+5hKlSpJmTJlZPv27SZs6b1eWFmDlkUDVK9evczoWO3atc0xnuewjtERrtRcu3bN3CyxsbHmXtujt0BktStD7XOF29EQ358ziGSqn+AX9JUz0E/OQV85A/3kHPSV/2TkM89w2Nq3b1+yKYSFCxc2I0c3q1SYkgMHDphwpeuzdF2WrgOrUqWKmfKnI1P58uXzOl6D1enTp83Peu8ZtKz91r60jtEAdeXKFYmJiUnWpnHjxsmoUaOSbdeRNF0bFsjWrl2bgaMb+74BK1f6/pxBKGP9BH+ir5yBfnIO+soZ6CfnoK+yXlxcnH1ha8OGDeJLFStWNMHq4sWL8sknn5jRMV2f5U9Dhw71GsHTYFa6dGlp1aqVKeQRqAlb/7G1bNlSIiMj0/ekVYN935A2E3x/ziCSqX6CX9BXzkA/OQd95Qz0k3PQV/5jzXqzdc3WuXPn5MiRI+7ApKNbmaGjV1ohUNWtW1d2794t06ZNM9UNtfCFlpT3HN3SaoTFihUzP+v9rl27vM5nVSv0PCZpBUN9rKEppVEtpVUL9ZaU/iIH+i9zhtoYlmBHA3x/ziDkhN8l/B/6yhnoJ+egr5yBfnIO+irrZeTzznA1Ql1TpdMFixcvLo0aNTK3EiVKSPfu3TM0pJaaxMREs15Kg5e+kfXr17v3abjTUu867VDpvU5DPHv2rPsYTfgapHQqonWM5zmsY6xzAAAAAIAdMhy2dHqdTvPTkuw66qS3zz77zGwbMGBAhqfrbd68WX766ScTmvSxXhOrY8eOkjdvXhPg9PV06qIWzOjatasJSdb1vHRan4aqzp07yzfffGPKuQ8bNsxcm8samerZs6e5DtigQYPku+++k5kzZ8qiRYtMWXkAAAAAsEuGpxF++umnZm1VkyZN3Nvuu+8+MyXvscceM6Xb00tHpJ5++mlTzVDDlV7gWAOTzj1VWp5dC3DoxYx1tEurCGpYsoSHh5uLKmv1QQ1hOXPmNGu+Xn31VfcxWvZdKyVquNLpiVpS/t1336XsOwAAAIDACls6VTBpdT9VpEiRDE8j1OtopSU6OlpmzJhhbqkpW7asrLxJBTwNhkmrKAIAAABAQE0j1BGkESNGmFLtFi2hrqXSWQcFAAAAAJkc2dKpeDoFT6fj1axZ02zT9VI6CqVTAAEAAAAAmQhb1apVk6NHj8r8+fNNwQn15JNPmqIWqZVSBwAAAIBQk6nrbOXIkUOeffZZ37cGAAAAAEJtzZaWXm/atGmKV0y+ePGi2afTCQEAAAAAGQhbkydPlmbNmpkLBielZdu1XPukSZN83T4AAAAACO6wtXPnTnnwwQdT3d+uXTvZtm2br9oFAAAAAKERtn7++WfJnTt3qvtz5cplLk4MAAAAAMhA2CpcuLAcOXIk1f1ambBQoUK+ahcAAAAAhEbYatGihYwdOzbFfS6Xy+zTYwAAAAAAGSj9PmzYMKlbt67Ur19fBgwYIBUrVnSPaGnxjO+//17mzp1rZ1sBAAAAIPjCVvny5WXdunXyzDPPyBNPPCFhYWHuUa0qVarI2rVrpUKFCna2FQAAAACC86LG9erVk4MHD8r+/fvl6NGjJmj96U9/klq1atnXQgAAAAAI9rBl0XBFwAIAAAAAHxTIAAAAAACkH2ELAAAAAGxA2AIAAACAQAhbJ06cMIUxktJtug8AAAAAkImwVa5cOTl37lyy7efPnzf7AAAAAACZCFs6gmVdY8vTpUuXJDo62lftAgAAAIDQKP3ev39/c69B65VXXpEcOXK49yUkJMjOnTspBw8AAAAAGQ1b+/btc49sHThwQKKiotz79OeaNWvKwIED03s6AAAAAAhq6Q5bGzZsMPddu3aVadOmSZ48eexsFwAAAACERtiyzJkzx56WAAAAAEAoh63Lly/L+PHjZf369XL27FlJTEz02n/s2DFftg8AAAAAQiNs9ejRQzZt2iSdO3eW4sWLp1iZEAAAAABCXYbD1qpVq2TFihXSsGFDe1oEAAAAAKF4na38+fNLgQIF7GkNAAAAAIRq2Bo9erQMHz5c4uLi7GkRAAAAAITiNMLJkyfLjz/+KEWLFpXbbrtNIiMjvfZ//fXXvmwfAAAAAIRG2Grfvr09LQEAAACAUA5bI0aMsKclAAAAABDKa7YAAAAAAD4a2dLqg99//70UKlTIVCNM69pa58+fT88pAQAAACCopStsvfHGG5I7d27z89SpU+1uEwAAAACERtjq0qVLij8DAAAAAHxUIEMlJCTI0qVL5dtvvzWPq1atKg888ICEh4dn5nQAAAAAEHQyHLZ++OEHue++++Tnn3+WihUrmm3jxo2T0qVLy4oVK6R8+fJ2tBMAAAAAgrsa4YsvvmgC1cmTJ80FjPV24sQJKVeunNkHAAAAAMjEyNamTZtkx44dpkKhpWDBgjJ+/Hhp2LChr9sHAAAAAKExspU9e3b5448/km2/dOmSREVF+apdAAAAABBaYev++++X5557Tnbu3Ckul8vcdKSrZ8+epkgGAAAAACATYevNN980a7YaNGgg0dHR5qbTBytUqCDTpk2zp5UAAAAAEOxrtvLlyyefffaZqUpolX6vXLmyCVsAAAAAgAyGrcTERJk0aZJ8/vnnEh8fL82bN5cRI0ZITExMek8BAAAAACEj3dMIx44dKy+//LLkypVLSpYsaaYM9u7d297WAQAAAECwh60PPvhAZs6cKatXr5alS5fKsmXLZP78+WbECwAAAACQyWmEeuHi++67z/24RYsWEhYWJqdOnZJSpUql9zQIdsv6pL2/HUVUAAAAEBrSPbJ148YNU3nQU2RkpFy/ft2OdgEAAABAaIxs6fW0nnnmGXNRY8vVq1fN9bVy5szp3rZ48WLftxIAAAAAgjVsdenSJdm2Tp06+bo9AAAAABBaYWvOnDn2tgQAAAAAQnHNFgAAAAAg/QhbAAAAAGADwhYAAAAA2ICwBQAAAAA2IGwBAAAAgA0IWwAAAABgA8IWAAAAAARb2Bo3bpzceeedkjt3bilSpIi0b99ejhw54nXM1atXpXfv3lKwYEHJlSuXdOjQQc6cOeN1zIkTJ6Rt27aSI0cOc56XXnpJbty44XXMxo0bpU6dOpI9e3apUKGCzJ07N0veIwAAAIDQ5NewtWnTJhOkduzYIWvXrpXr169Lq1at5PLly+5j+vXrJ8uWLZOPP/7YHH/q1Cl5+OGH3fsTEhJM0IqPj5dt27bJvHnzTJAaPny4+5jjx4+bY5o2bSr79++Xvn37So8ePWT16tVZ/p4BAAAAhIYIf774F1984fVYQ5KOTO3du1caNWokFy9elPfee08WLFggzZo1M8fMmTNHKleubALa3XffLWvWrJHDhw/LunXrpGjRolKrVi0ZPXq0DB48WEaOHClRUVEye/ZsKVeunEyePNmcQ5+/ZcsWeeONN6R169Z+ee8AAAAAgptfw1ZSGq5UgQIFzL2GLh3tatGihfuYSpUqSZkyZWT79u0mbOl99erVTdCyaIDq1auXHDp0SGrXrm2O8TyHdYyOcKXk2rVr5maJjY0199oWvQUiq10Zap8rXLJcgH5+Ad1PWW3V4NT3tZkgocIRfQX6yUHoK2egn5yDvvKfjHzmARO2EhMTTfhp2LChVKtWzWw7ffq0GZnKly+f17EarHSfdYxn0LL2W/vSOkZD1JUrVyQmJibZWrJRo0Yla6OOoum6sECm0zHTr7FkuZUrs/41Hd9PWS2N34sQ7L/A7itY6CfnoK+cgX5yDvoq68XFxTkvbOnarYMHD5rpff42dOhQ6d+/v/uxhrLSpUub9WR58uSRQE3Y+o+tZcuWEhkZeesjGHYJoZERn/VTVmNkyzl9BfrJQegrZ6CfnIO+8h9r1ptjwtbzzz8vy5cvl82bN0upUqXc24sVK2YKX1y4cMFrdEurEeo+65hdu3Z5nc+qVuh5TNIKhvpYg1PSUS2lFQv1lpT+Igf6L3OG2hiWIFkuwD+/rBLQv0tp/V4EaptDta/gRj85B33lDPSTc9BXWS8jn7dfqxG6XC4TtJYsWSJffvmlKWLhqW7duubNrF+/3r1NS8NrqfcGDRqYx3p/4MABOXv2rPsYTfkapKpUqeI+xvMc1jHWOQAAAADA1yL8PXVQKw1+9tln5lpb1hqrvHnzmhEnve/evbuZ0qdFMzRAvfDCCyYkaXEMpVP7NFR17txZJk6caM4xbNgwc25rdKpnz57y1ltvyaBBg6Rbt24m2C1atEhWrFghQW9ZH3+3AAAAAAhJfh3ZmjVrlqlA2KRJEylevLj7tnDhQvcxWp79/vvvNxcz1nLwOiVw8eLF7v3h4eFmCqLeawjr1KmTPP300/Lqq6+6j9ERMw1WOppVs2ZNUwL+3Xffpew7AAAAgOAc2dJphDcTHR0tM2bMMLfUlC1bVlbepEqaBrp9+/Zlqp0AAAAA4KiRLQAAAAAIVoQtAAAAALABYQsAAAAAbEDYAgAAAAAbELYAAAAAwAaELQAAAACwAWELAAAAAGxA2AIAAAAAGxC2AAAAAMAGhC0AAAAAsAFhCwAAAABsQNgCAAAAABsQtgAAAADABoQtAAAAALABYQsAAAAAbEDYAgAAAAAbELYAAAAAwAYRdpwUSNWyPqnvazctK1sCAAAA2IqRLQAAAACwAWELAAAAAGxA2AIAAAAAGxC2AAAAAMAGhC0AAAAAsAFhCwAAAABsQNgCAAAAABsQtgAAAADABoQtAAAAALABYQsAAAAAbEDYAgAAAAAbELYAAAAAwAYRdpwUyJRlfVLf125aVrYEAAAAuGWMbAEAAACADRjZgjMw6gUAAACHYWQLAAAAAGxA2AIAAAAAGxC2AAAAAMAGhC0AAAAAsAFhCwAAAABsQNgCAAAAABsQtgAAAADABoQtAAAAALABYQsAAAAAbEDYAgAAAAAbRNhxUgBBalmf1Pe1m5aVLQEAAAh4jGwBAAAAgA0Y2YLzMdoCAACAAETYAkJRWgEVAAAAPsE0QgAAAACwAWELAAAAAGxA2AIAAAAAGxC2AAAAAMAGhC0AAAAAsAFhCwAAAABsQNgCAAAAABtwnS0ENy54DAAAAD9hZAsAAAAAbEDYAgAAAAAbELYAAAAAwAaELQAAAAAItrC1efNmadeunZQoUULCwsJk6dKlXvtdLpcMHz5cihcvLjExMdKiRQs5evSo1zHnz5+Xjh07Sp48eSRfvnzSvXt3uXTpktcx//73v+Uvf/mLREdHS+nSpWXixIlZ8v4AAAAAhC6/hq3Lly9LzZo1ZcaMGSnu11D05ptvyuzZs2Xnzp2SM2dOad26tVy9etV9jAatQ4cOydq1a2X58uUmwD333HPu/bGxsdKqVSspW7as7N27VyZNmiQjR46Ut99+O0veIwAAAIDQ5NfS723atDG3lOio1tSpU2XYsGHy4IMPmm0ffPCBFC1a1IyAPfHEE/Ltt9/KF198Ibt375Z69eqZY6ZPny733XefvP7662bEbP78+RIfHy/vv/++REVFSdWqVWX//v0yZcoUr1AGAAAAACFxna3jx4/L6dOnzdRBS968eaV+/fqyfft2E7b0XqcOWkFL6fHZsmUzI2EPPfSQOaZRo0YmaFl0dGzChAny+++/S/78+ZO99rVr18zNc3RMXb9+3dwCkdWuZO1zhfunQU7gh75MtZ+ymh2/F/5+T8HaV0gT/eQc9JUz0E/OQV/5T0Y+84ANWxq0lI5kedLH1j69L1KkiNf+iIgIKVCggNcx5cqVS3YOa19KYWvcuHEyatSoZNvXrFkjOXLkkECm0ym9NfZTSxxg5coA6qes1jioPs/g7iukB/3kHPSVM9BPzkFfZb24uDjnhy1/Gjp0qPTv399rZEsLa+jaLy3EEagJW/+xtWzZUiIjI///jlWD/dmswNZmQuD0U1az4/fCD59nSPQV0kQ/OQd95Qz0k3PQV/5jzXpzdNgqVqyYuT9z5oypRmjRx7Vq1XIfc/bsWa/n3bhxw1QotJ6v9/ocT9Zj65iksmfPbm5J6S9yoP8yJ2tjWII/mxPY/NiXfv9dsuP3IsD/bTi2r5Au9JNz0FfOQD85B32V9TLyeQfsdbZ06p+GofXr13ulSF2L1aBBA/NY7y9cuGCqDFq+/PJLSUxMNGu7rGO0QqHn3Er9K0DFihVTnEIIAAAAAL7g17Cl18PSyoB6s4pi6M8nTpww193q27evjBkzRj7//HM5cOCAPP3006bCYPv27c3xlStXlnvvvVeeffZZ2bVrl2zdulWef/55UzxDj1NPPfWUKY6h19/SEvELFy6UadOmeU0TBAAAAABf8+s0wj179kjTpk3dj60A1KVLF5k7d64MGjTIXItLS7TrCNaf//xnU+pdL05s0dLuGrCaN29uqhB26NDBXJvLs4KhFrbo3bu31K1bVwoVKmQulEzZd8iyPqnvazctK1sCAACAIOTXsNWkSRNzPa3U6OjWq6++am6p0cqDCxYsSPN1atSoIV999dUttRUAAAAAMiJg12wBAAAAgJMRtgAAAADABoQtAAAAALABYQsAAAAAbBCwFzUG/IpKhQAAALhFjGwBAAAAgA0IWwAAAABgA8IWAAAAANiAsAUAAAAANiBsAQAAAIANCFsAAAAAYAPCFgAAAADYgLAFAAAAADYgbAEAAACADQhbAAAAAGADwhYAAAAA2ICwBQAAAAA2IGwBAAAAgA0IWwAAAABggwg7TgoEtWV9Ut/XbpqELD4XAAAAL4xsAQAAAIANCFsAAAAAYAPCFgAAAADYgDVbgC+xbgkAAAD/w8gWAAAAANiAsAUAAAAANiBsAQAAAIANCFsAAAAAYAPCFgAAAADYgLAFAAAAADYgbAEAAACADQhbAAAAAGADLmoMZOUFj13hItJYZNVgkbCE/7+PCx4DAAAEHcIWEChBLDUEMQAAAEcibAGhGOAAAABgO9ZsAQAAAIANCFsAAAAAYAPCFgAAAADYgLAFAAAAADYgbAEAAACADahGCDgZFQcBAAACFmELCHQEKgAAAEdiGiEAAAAA2ICwBQAAAAA2YBohAP9OhWw3LStbAgAAkGUY2QIAAAAAGxC2AAAAAMAGhC0AAAAAsAFhCwAAAABsQNgCAAAAABtQjRCAf1GpEAAABClGtgAAAADABoQtAAAAALAB0wiDZRqWK1xEGousGiwSluDvFgG+wRRDAADgYIxsAQAAAIANCFsAAAAAYAOmEQJwJqYYAgCAAEfYAhB8CGIAACAAMI0QAAAAAGwQUiNbM2bMkEmTJsnp06elZs2aMn36dLnrrrv83SwAgTLq5Slphc+0RsQYSQMAAKEcthYuXCj9+/eX2bNnS/369WXq1KnSunVrOXLkiBQpUsTfzQMQLCHNV89LSyAFOKcETae0EwAQVEImbE2ZMkWeffZZ6dq1q3msoWvFihXy/vvvy5AhQ/zdPADwb4BzSvhJ+jzPEcgHpviufQAA+EBIhK34+HjZu3evDB061L0tW7Zs0qJFC9m+fXuy469du2ZulosXL5r78+fPy/Xr1yXgXL4h110uiZM42fDf3ySb64bULpPP361CCqx++k1uSCQXnw5o9JUPfNTb9ud59VMWvB4y77orXOKkgfz2yYCs+zfV8tXU960d7vtzOkUa7/16k1ckLi5OfvvtN4mMjPTJOW3po8z2Q2bbGYC/E/qdNFN9hVv2xx9/mHuXy3XTY8Nc6TnK4U6dOiUlS5aUbdu2SYMGDdzbBw0aJJs2bZKdO3d6HT9y5EgZNWqUH1oKAAAAwAlOnjwppUqVSvOYkBjZyigdAdP1XZbExEQzqlWwYEEJCwuTQBQbGyulS5c2nZ4nTx5/NwepoJ+cg75yBvrJOegrZ6CfnIO+8h8dq9LRrRIlStz02JAIW4UKFZLw8HA5c+aM13Z9XKxYsWTHZ8+e3dw85cvnjGl5+o+Nf3CBj35yDvrKGegn56CvnIF+cg76yj/y5s2bruNC4jpbUVFRUrduXVm/fr3XaJU+9pxWCAAAAAC+EhIjW0qnBXbp0kXq1atnrq2lpd8vX77srk4IAAAAAL4UMmHr8ccfl3Pnzsnw4cPNRY1r1aolX3zxhRQtWlSCgU57HDFiRLLpjwgs9JNz0FfOQD85B33lDPSTc9BXzhAS1QgBAAAAIKuFxJotAAAAAMhqhC0AAAAAsAFhCwAAAABsQNgCAAAAABsQtoLAjBkz5LbbbpPo6GipX7++7Nq1y99NQhLjxo2TO++8U3Lnzi1FihSR9u3by5EjR/zdLNzE+PHjJSwsTPr27evvpiAFP//8s3Tq1EkKFiwoMTExUr16ddmzZ4+/mwUPCQkJ8sorr0i5cuVMH5UvX15Gjx4t1Obyv82bN0u7du2kRIkS5r9zS5cu9dqvfaQVnIsXL276rkWLFnL06FG/tTdUpdVP169fl8GDB5v/9uXMmdMc8/TTT8upU6f82mZ4I2w53MKFC801xLT059dffy01a9aU1q1by9mzZ/3dNHjYtGmT9O7dW3bs2CFr1641/4Fs1aqVudYbAtPu3bvlH//4h9SoUcPfTUEKfv/9d2nYsKFERkbKqlWr5PDhwzJ58mTJnz+/v5sGDxMmTJBZs2bJW2+9Jd9++615PHHiRJk+fbq/mxby9P8/+p1B/2CbEu2nN998U2bPni07d+40X+b1+8XVq1ezvK2hLK1+iouLM9/99A8aer948WLzh9wHHnjAL21Fyij97nA6kqUjJvo/MpWYmCilS5eWF154QYYMGeLv5iEVes03HeHSENaoUSN/NwdJXLp0SerUqSMzZ86UMWPGmOvy6YXQETj0v29bt26Vr776yt9NQRruv/9+cz3L9957z72tQ4cOZqTkww8/9Gvb8P/piMmSJUvMrAulXw11lGTAgAEycOBAs+3ixYumL+fOnStPPPGEn1scmpL2U2p/KLzrrrvkP//5j5QpUyZL24eUMbLlYPHx8bJ3714ztG/Jli2bebx9+3a/tg1p0/9pqQIFCvi7KUiBjkK2bdvW698WAsvnn38u9erVk0cffdT84aJ27dryzjvv+LtZSOKee+6R9evXy/fff28ef/PNN7JlyxZp06aNv5uGNBw/flxOnz7t9d/AvHnzmj/w8v0i8L9faCjLly+fv5uC/4mwfoDz/Prrr2Y+vP6lyZM+/u677/zWLqRNRx91DZBOgapWrZq/m4MkPvroIzMdQ/86iMB17NgxMz1Np1G//PLLpr9efPFFiYqKki5duvi7efAYgYyNjZVKlSpJeHi4+X/W2LFjpWPHjv5uGtKgQUul9P3C2ofAo1M8dQ3Xk08+KXny5PF3c/A/hC3AD6MmBw8eNH/dRWA5efKk9OnTx6yr04IzCOw/WujI1muvvWYe68iW/rvS9SWErcCxaNEimT9/vixYsECqVq0q+/fvN39s0ilq9BPgO7oW/LHHHjNTQPUPUQgcTCN0sEKFCpm/FJ45c8Zruz4uVqyY39qF1D3//POyfPly2bBhg5QqVcrfzUESOi1Xi8voeq2IiAhz03V1ukhcf9a/yiMwaIW0KlWqeG2rXLmynDhxwm9tQnIvvfSSGd3SNT5aMa1z587Sr18/U6EVgcv6DsH3C2cFLV2npX8sZFQrsBC2HEyny9StW9fMh/f8a68+btCggV/bBm/6lyYNWrqw9csvvzRlkBF4mjdvLgcOHDB/fbduOnqiU570Z/*************************************/Hen/qxC49P9RGqo8v1/odFCtSsj3i8AMWlqWf926deZSGAgsTCN0OF2voFMx9AuhVp/RimlaJrRr167+bhqSTB3UaTSfffaZudaWNeddFxxrVS4EBu2bpOvotNyx/s+L9XWBRUdHtPiCTiPULxp6fcG3337b3BA49PpAukZLq6LpNMJ9+/bJlClTpFu3bv5uWsjTqqs//PCDV1EM/aOSFm7S/tLpnlqN9Y477jDhS8uL6/TPtCrhIWv7SUf4H3nkEbPOWGfN6OwL6/uF7tc/yiMAaOl3ONv06dNdZcqUcUVFRbnuuusu144dO/zdJCSh/9RSus2ZM8ffTcNNNG7c2NWnTx9/NwMpWLZsmatatWqu7NmzuypVquR6++23/d0kJBEbG2v+/ej/o6Kjo12333676+9//7vr2rVr/m5ayNuwYUOK/1/q0qWL2Z+YmOh65ZVXXEWLFjX/xpo3b+46cuSIv5sdctLqp+PHj6f6/UKfh8DAdbYAAAAAwAas2QIAAAAAGxC2AAAAAMAGhC0AAAAAsAFhCwAAAABsQNgCAAAAABsQtgAAAADABoQtAAAAALABYQsAAAAAbEDYAgA41pEjR6RYsWLyxx9/pPs5zzzzjLRv314CwW233SZTp0716TmfeOIJmTx5sk/PCQDIHMIWAMBvNm7cKGFhYanemjZtmubzhw4dKi+88ILkzp37puc7ffq0+MvcuXMlX758ybbv3r1bnnvuOZ++1rBhw2Ts2LFy8eJFn54XAJBxhC0AgN/cc8898ssvvyS7/eMf/zAB6W9/+1uqzz1x4oQsX77cjFSlNOKV9JxFihSRQFO4cGHJkSOHT89ZrVo1KV++vHz44Yc+PS8AIOMIWwAAv4mKijLTAD1vv//+uwwcOFBefvllefTRR1N97qJFi6RmzZpSsmTJZPs0WCU9b7ZsKf8vLzExUcaNGyflypWTmJgYc85PPvnEva9UqVIya9Ysr+fs27fPnO8///mPeTxlyhSpXr265MyZU0qXLm1C4qVLl9yjbV27djUjTdYo28iRI1OcRqgB8sEHH5RcuXJJnjx55LHHHpMzZ8649+vzatWqJf/85z/Nc/PmzWumDSadRtmuXTv56KOP0tUHAAD7ELYAAAHjwoULJmw0adJERo8eneaxX331ldSrV++WX1OD1gcffCCzZ8+WQ4cOSb9+/aRTp06yadMmE6iefPJJWbBggddz5s+fLw0bNpSyZcuax3rcm2++aZ4/b948+fLLL2XQoEHu0TsNVBqerFE2DZNJabDT937+/Hnz2mvXrpVjx47J448/7nXcjz/+KEuXLjWjenrTY8ePH+91zF133SW7du2Sa9eu3fLnAwDIvIhbeC4AAD6jYeOpp56SiIgIE2Z0BCgtOqqUWtjS0ShPGoo0CCWlYeS1116TdevWSYMGDcy222+/XbZs2WKmMjZu3Fg6duxoCk7oqFOZMmVMO3XUSNdGWfr27ev+WUecxowZIz179pSZM2ea0TsdgdL3oyNsqVm/fr0cOHBAjh8/bkbHlIbAqlWrmrVdd955p/tz0jVg1jq1zp07m+fqOi1LiRIlJD4+3qxTswIhACDrEbYAAAFBpw1u377djMhYQSItV65ckejo6FRHvTzPERkZmeJxP/zwg8TFxUnLli29tmtQqV27tvlZp+1VrlzZjG4NGTLEjCSdPXvWa4qjhjUdIfvuu+8kNjZWbty4IVevXjXnTu+arG+//daELCtoqSpVqpjCGrrPClsa5jzfW/HixU17POl0SKWvDwDwH8IWAMDvdKTo9ddflxUrVsgdd9yRrucUKlTIrO9Kia6/Sqn6X1LWuip93aRrv7Jnz+7+WUe3rLCl9/fee68ULFjQ7Pvpp5/k/vvvl169epnRpQIFCpiRse7du5vQ5usCGEmDo46Y6WiXJ52KaBXgAAD4D2ELAOBX+/fvN8FE1x21bt063c/TkafDhw/f0mvryJGGKp0iqFMGU6PTG3Xa4N69e03xDF3fZdFtGnZ0qqFVhEOLd3jSqYQJCQlptkVHz06ePGlu1uiWvj9dx6btzIiDBw+aqZQaSAEA/kPYAgD4za+//mouMKwFMbQoRdJrYYWHh6c6OqPBrEePHibE6HGedFqdTuPzpCNRSUeFdDqeFqvQohgamP785z+bqoFbt241BS26dOninrqnhS40FOrrPfDAA+5zVKhQQa5fvy7Tp083VQD1uZ5hzHq+jqLp2iqtdqijXUlHvFq0aGEqGuoomhbU0KmIWtVQQ2BGC4HoNMpWrVpl6DkAAN+jGiEAwG90+p4Wuli5cqVZe5T0Zq1TSkmbNm1MMQ1dL5VUxYoVk51LR6BSolUPX3nlFbPmSkeXdIqgtkunInrSEPTNN9/IQw895F4TpTQ8aen3CRMmmGtcaXEPPZcnDWpaMEMrC2p4nDhxYrJ26HTAzz77TPLnzy+NGjUy4UuLdSxcuFAyQkOmVit89tlnM/Q8AIDvhblcLpcN5wUAwHYzZsyQzz//XFavXu3vpgQMvSbYkiVLZM2aNf5uCgCEPKYRAgAc669//atZ06QX9U1PBcNQoFMldUojAMD/GNkCAAAAABuwZgsAAAAAbEDYAgAAAAAbELYAAAAAwAaELQAAAACwAWELAAAAAGxA2AIAAAAAGxC2AAAAAMAGhC0AAAAAsAFhCwAAAADE9/4fKPM/1lCTU4UAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Height (Z) Distribution Plot\n", "from matplotlib import pyplot as plt\n", "\n", "plt.figure(figsize=(10, 5))\n", "plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')\n", "plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')\n", "plt.legend()\n", "plt.title(\"Z-Height Distribution\")\n", "plt.xlabel(\"Z (Elevation)\")\n", "plt.ylabel(\"Point Count\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# Save parameters to JSON for reproducibility\n", "import json\n", "\n", "parameters = {\n", "    \"run_info\": {\n", "        \"timestamp\": timestamp,\n", "        \"site_name\": site_name,\n", "        \"project_type\": project_type,\n", "        \"method\": \"PMF\",\n", "        \"notebook_version\": \"1.1\"\n", "    },\n", "    \"pmf_parameters\": {\n", "        \"cell_size\": cell_size,\n", "        \"max_window_size\": max_window_size,\n", "        \"slope\": slope,\n", "        \"max_distance\": max_distance,\n", "        \"initial_distance\": initial_distance,\n", "        \"height_threshold_ratio\": height_threshold_ratio\n", "    },\n", "    \"processing_parameters\": {\n", "        \"buffer_radius\": buffer_radius\n", "    },\n", "    \"paths\": {\n", "        \"input_path\": str(input_path),\n", "        \"output_path\": str(ground_seg_path),\n", "        \"run_output_path\": str(analysis_output_path)\n", "    }\n", "}\n", "\n", "# Save parameters to file\n", "params_file = analysis_output_path / \"parameters.json\"\n", "with open(params_file, 'w') as f:\n", "    json.dump(parameters, f, indent=2)\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 14:49:35,193 - INFO - PMF Ground Segmentation - Ready!\n", "2025-07-04 14:49:35,194 - INFO - ==================================================\n", "2025-07-04 14:49:35,195 - INFO - Project: ENEL/trino_enel\n", "2025-07-04 14:49:35,195 - INFO - --------------------------------------------------\n", "2025-07-04 14:49:35,195 - INFO - Input path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised.ply\n", "2025-07-04 14:49:35,196 - INFO - Output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation\n", "2025-07-04 14:49:35,196 - INFO - Current run output: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/analysis_output/trino_enel\n", "2025-07-04 14:49:35,197 - INFO - PMF Parameters: cell_size=1.0m, max_window=33, slope=0.15\n"]}], "source": ["# Final readiness logger.info\n", "logger.info(\"PMF Ground Segmentation - Ready!\")\n", "logger.info(\"=\" * 50)\n", "logger.info(f\"Project: {project_type}/{site_name}\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"Input path: {input_path}\")\n", "logger.info(f\"Output path: {ground_seg_path}\")\n", "logger.info(f\"Current run output: {analysis_output_path}\")\n", "logger.info(f\"PMF Parameters: cell_size={cell_size}m, max_window={max_window_size}, slope={slope}\")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# Visualize the results\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "# Create point cloud for ground\n", "pcd_ground = o3d.geometry.PointCloud()\n", "pcd_ground.points = o3d.utility.Vector3dVector(ground_points)\n", "pcd_ground.paint_uniform_color([0.0, 1.0, 0.0])  # Green\n", "\n", "# Create point cloud for non-ground\n", "pcd_nonground = o3d.geometry.PointCloud()\n", "pcd_nonground.points = o3d.utility.Vector3dVector(nonground_points)\n", "pcd_nonground.paint_uniform_color([1.0, 0.0, 0.0])  # Red\n", "\n", "# Show both together\n", "o3d.visualization.draw_geometries([pcd_ground, pcd_nonground],\n", "                                  window_name=\"Ground vs Non-Ground\",\n", "                                  point_show_normal=False)\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 14:50:02,055 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/pmf/trino_enel_ground.ply\n", "2025-07-04 14:50:02,061 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/pmf/trino_enel_nonground.ply\n"]}], "source": ["# Satisfied with the results save the final output for next stage \n", "save_ply(ground_seg_path / f\"{method_name}/{site_name}_ground.ply\", ground_points)\n", "save_ply(ground_seg_path / f\"{method_name}/{site_name}_nonground.ply\", nonground_points)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}
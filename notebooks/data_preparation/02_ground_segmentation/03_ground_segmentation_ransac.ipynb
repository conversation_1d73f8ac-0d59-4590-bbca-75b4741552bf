{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation using RANSAC Method\n", "\n", "This notebook implements RANSAC-based ground segmentation for point cloud processing in solar array inspection projects.\n", "\n", "**Method**: RANSAC (Random Sample Consensus) Plane Fitting  \n", "**Input Data**: Raw point cloud (.las, .laz, .pcd)  \n", "**Output**: Ground and non-ground point clouds with RANSAC-specific naming  \n", "**Format**: .ply files with method-specific prefixes  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As Built Analytics for Solar Array Inspection\n", "\n", "## RANSAC Algorithm Overview\n", "\n", "RANSAC is a robust iterative method for fitting mathematical models to data containing outliers:\n", "\n", "**Strengths:**\n", "- Highly robust to outliers and noise\n", "- Works well on relatively flat terrain\n", "- Fast convergence for simple planar surfaces\n", "- Deterministic results with fixed random seed\n", "\n", "**Limitations:**\n", "- Assumes ground is a single plane\n", "- May struggle with complex terrain variations\n", "- Performance depends on parameter tuning\n", "- Can misclassify low vegetation as ground\n", "\n", "**Best Use Cases:**\n", "- Flat or gently sloping terrain\n", "- Areas with minimal vegetation\n", "- Quick initial ground removal\n", "- Preprocessing for other algorithms"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration Parameters\n", "\n", "These parameters control the RANSAC algorithm behavior and can be adjusted via Papermill for different sites and conditions."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RANSAC Plane Segmentation – Parameter Documentation\n", "\n", "**RANSAC (Random Sample Consensus)** is a robust model-fitting algorithm widely used in point cloud processing to detect geometric primitives such as planes. It iteratively selects random subsets of points to hypothesize models (planes), then verifies how many points in the full dataset support that model.\n", "\n", "This makes RANSAC highly resilient to noise and outliers, which is crucial for segmenting surfaces like solar tables, ground slabs, walls, and support beams in unstructured 3D scans.\n", "\n", "Below are the tunable parameters used in our RANSAC implementation:\n", "\n", "\n", "## Use Case Context\n", "\n", "In our pipeline, RANSAC is typically used for:\n", "- Extracting dominant horizontal planes (e.g., solar slab foundations).\n", "- Isolating vertical planes (e.g.,  piles or support poles).\n", "- Pre-filtering large flat regions before deviation or surface analysis.\n", "\n", "It works especially well post-CSF or PMF, once the ground/non-ground segmentation is complete.\n", "\n", "---\n", "\n", "## Source and References\n", "\n", "These parameters and algorithm behavior are based on standard implementations and research:\n", "\n", "- **<PERSON><PERSON><PERSON>, M. <PERSON>, & <PERSON>, R. C. (1981)**  \n", "  *Random sample consensus: A paradigm for model fitting with applications to image analysis and automated cartography.*  \n", "  [DOI:10.1016/S0004-370]()\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "\n", "# Papermill parameters - these will be injected by Papermill\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "method_name = \"ransac\"  # Options: \"csf\", \"pmf\", \"ransac\"\n", "timestamp = None  # Auto-generated if None\n", "\n", "# Standardized paths\n", "input_data_path = get_processed_data_path(site_name, \"denoising\")\n", "output_path = get_output_path(f\"ground_segmentation_{method_name}\", site_name, timestamp)\n", "mlflow_tracking_uri = get_mlflow_tracking_uri()\n", "coordinate_system = \"EPSG:32633\"\n", "\n", "# Find input file dynamically\n", "input_point_cloud = find_latest_file(input_data_path, \"*denoised*.ply\")\n", "point_cloud_path = str(input_point_cloud)\n", "\n", "# MLflow configuration\n", "mlflow_experiment_name = f\"ground_segmentation_{project_type}\"\n", "mlflow_run_name = f\"{method_name}_{site_name}_{timestamp or 'auto'}\"\n", "\n", "\n", "# === RANSAC (Plane Segmentation) Parameters ===\n", "# These parameters control how RANSAC detects planar surfaces in noisy point clouds.\n", "\n", "buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)\n", "\n", "distance_threshold = 0.2  \n", "# Maximum distance (in meters) from a point to a candidate plane for it to be considered an inlier.\n", "# Smaller values yield tighter fitting planes but may miss noisy or partially flat regions.\n", "\n", "num_iterations = 1000  \n", "# Number of random sampling iterations to attempt.\n", "# More iterations increase the chance of finding the best-fitting plane.\n", "\n", "min_inliers_ratio = 0.05  \n", "# Minimum ratio of inliers (as a percentage of total points) required to accept a plane.\n", "# Helps filter out spurious or small patch detections.\n", "\n", "early_stop_ratio = 0.6  \n", "# If a plane is found that covers at least this ratio of total points, RANSAC will stop early.\n", "# Speeds up processing when large planar surfaces (e.g., ground or slabs) dominate.\n", "\n", "\n", "# === Processing Control Parameters ===\n", "# These help manage memory usage and performance for large point clouds.\n", "\n", "max_points_processing = 1000000  \n", "# Maximum number of points to process in memory at once.\n", "# If exceeded, the point cloud should be downsampled or processed in chunks."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Library Imports and Environment Setup\n", "\n", "Import required libraries for point cloud processing, visualization, and RANSAC implementation."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully. Random seed set to 42\n"]}], "source": ["# Import libraries\n", "import numpy as np\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from tqdm import tqdm\n", "\n", "# Set random seed for reproducible results\n", "random_seed = 42                 # Random seed for reproducible results\n", "np.random.seed(random_seed)\n", "\n", "print(f\"Libraries imported successfully. Random seed set to {random_seed}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Library Setup\n", "\n", "The RANSAC method requires minimal dependencies compared to other ground segmentation methods. The random seed ensures reproducible results across multiple runs, which is crucial for comparing different methods on the same dataset."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Logging Configuration\n", "\n", "Configure logging to track processing steps and performance metrics."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 14:55:10,643 - INFO - RANSAC Ground Segmentation initialized for site: trino_enel\n", "2025-07-04 14:55:10,645 - INFO - Parameters - Distance threshold: 0.2m, Iterations: 1000\n"]}], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "logger.info(f\"RANSAC Ground Segmentation initialized for site: {site_name}\")\n", "logger.info(f\"Parameters - Distance threshold: {distance_threshold}m, Iterations: {num_iterations}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Directory Structure and Path Configuration\n", "\n", "Set up input and output paths following the project organization structure."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 14:55:41,697 - INFO - Checking raw data path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel, Exists: True\n", "2025-07-04 14:55:41,698 - INFO - Using point cloud file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised.ply\n", "2025-07-04 14:55:41,699 - INFO - Ground segmentation output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation\n"]}], "source": ["# Standardized paths\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "from shared.utils import resolve_point_cloud_path\n", "\n", "# Raw data path\n", "raw_path = get_data_path(site_name, data_type=\"raw\")\n", "logger.info(f\"Checking raw data path: {raw_path}, Exists: {raw_path.exists()}\")\n", "\n", "# Timestamped output directory\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_path = get_output_path(notebook_type=\"denoising\", site_name=site_name, timestamp=timestamp)\n", "\n", "# Processed data path for this stage\n", "processed_path = get_processed_data_path(site_name, processing_stage=\"denoising\")\n", "\n", "# Analysis output path \n", "analysis_output_path = get_data_path(site_name, data_type=\"analysis_output\")\n", "\n", "# Determine input path\n", "input_path = processed_path / f\"{site_name}_denoised.ply\"\n", "point_cloud_file = resolve_point_cloud_path(input_path)\n", "\n", "# Ground segmentation output directory (organized by site/project)\n", "ground_seg_path = get_processed_data_path(site_name, processing_stage=\"ground_segmentation\")\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Ground segmentation output path: {ground_seg_path.resolve()}\")\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameters saved to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/analysis_output/trino_enel/parameters.json\n"]}], "source": ["from datetime import datetime\n", "import json\n", "\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "visualization_enabled = True\n", "\n", "# Save parameters to JSON for reproducibility\n", "parameters = {\n", "    \"run_info\": {\n", "        \"timestamp\": timestamp,\n", "        \"site_name\": site_name,\n", "        \"project_type\": project_type,\n", "        \"method\": \"RANSAC\",\n", "        \"notebook_version\": \"1.1\"\n", "    },\n", "    \"ransac_parameters\": {\n", "        \"distance_threshold\": distance_threshold,\n", "        \"num_iterations\": num_iterations,\n", "        \"min_inliers_ratio\": min_inliers_ratio,\n", "        \"early_stop_ratio\": early_stop_ratio\n", "    },\n", "    \"processing_parameters\": {\n", "        \"max_points_processing\": max_points_processing,\n", "        \"buffer_radius\": buffer_radius,\n", "        \"visualization_enabled\": visualization_enabled\n", "    },\n", "    \"paths\": {\n", "        \"input_path\": str(input_path),\n", "        \"output_path\": str(ground_seg_path),\n", "        \"run_output_path\": str(analysis_output_path)\n", "    }\n", "}\n", "\n", "# Save parameters to file\n", "params_file = analysis_output_path / \"parameters.json\"\n", "with open(params_file, 'w') as f:\n", "    json.dump(parameters, f, indent=2)\n", "\n", "print(f\"Parameters saved to: {params_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Path Configuration\n", "\n", "The RANSAC method uses method-specific output directory naming (ransac) to distinguish results from other ground segmentation methods. This ensures clear separation of outputs when comparing different approaches on the same dataset."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Point Cloud Data Loading\n", "\n", "Load the raw point cloud data from LAS/LAZ files and prepare for RANSAC processing."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 14:57:46,299 - INFO - Reading PLY file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised.ply\n", "2025-07-04 14:57:46,302 - INFO - Point cloud statistics:\n", "2025-07-04 14:57:46,302 - INFO - --------------------------------------------------\n", "2025-07-04 14:57:46,302 - INFO -   <PERSON>aded 36738 points\n", "2025-07-04 14:57:46,303 - INFO -   X range: 435221.67 to 436794.15 (1572.48m)\n", "2025-07-04 14:57:46,304 - INFO -   Y range: 5010816.92 to 5012539.06 (1722.14m)\n", "2025-07-04 14:57:46,304 - INFO -   Z range: -0.10 to 13.14 (13.24m)\n"]}], "source": ["# Load Point Cloud data\n", "import open3d as o3d\n", "\n", "# Load Point Cloud (.ply)\n", "input_path = Path(point_cloud_path)\n", "logger.info(f\"Reading PLY file: {input_path}\")\n", "\n", "pcd = o3d.io.read_point_cloud(str(input_path))\n", "\n", "if not pcd.has_points():\n", "    raise ValueError(\"Loaded PLY file contains no points.\")\n", "\n", "points = np.asarray(pcd.points)\n", "\n", "# Display basic statistics\n", "logger.info(f\"Point cloud statistics:\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Loaded {points.shape[0]} points\")\n", "\n", "x, y, z = points[:, 0], points[:, 1], points[:, 2]\n", "\n", "logger.info(f\"  X range: {x.min():.2f} to {x.max():.2f} ({x.max()-x.min():.2f}m)\")\n", "logger.info(f\"  Y range: {y.min():.2f} to {y.max():.2f} ({y.max()-y.min():.2f}m)\")\n", "logger.info(f\"  Z range: {z.min():.2f} to {z.max():.2f} ({z.max()-z.min():.2f}m)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Data Loading\n", "\n", "The point cloud has been successfully loaded. The coordinate ranges provide insight into the terrain characteristics that will affect RANSAC performance. Large Z-ranges may indicate complex terrain that could challenge the single-plane assumption of RANSAC."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RANSAC Ground Plane Detection\n", "\n", "Execute the core RANSAC algorithm to identify the dominant ground plane in the point cloud."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 14:57:50,449 - INFO - Running RANSAC ground detection...\n", "2025-07-04 14:57:50,451 - INFO - Parameters: threshold=0.2m, iterations=1000, min_ratio=0.05\n", "RANSAC iterations: 100%|██████████| 1000/1000 [00:00<00:00, 1976.71it/s]\n", "2025-07-04 14:57:50,968 - INFO - RANSAC completed in 0.52 seconds\n", "2025-07-04 14:57:50,968 - INFO - Ground points: 14,014 (0.38 ratio)\n", "2025-07-04 14:57:50,968 - INFO - Plane: -0.000x + 0.000y + 1.000z + -124.370 = 0\n"]}], "source": ["import numpy as np\n", "import time\n", "from tqdm import tqdm\n", "\n", "logger.info(f\"Running RANSAC ground detection...\")\n", "logger.info(f\"Parameters: threshold={distance_threshold}m, iterations={num_iterations}, min_ratio={min_inliers_ratio}\")\n", "\n", "# Downsample the denoised point cloud before RANSAC\n", "# Purpose: Reduce computation, avoid overfitting, and improve speed\n", "max_points_ransac = 1_000_000\n", "if points.shape[0] > max_points_ransac:\n", "    logger.info(f\"Downsampling point cloud from {points.shape[0]:,} to {max_points_ransac:,} points for RANSAC\")\n", "    points = points[np.random.choice(points.shape[0], size=max_points_ransac, replace=False)]\n", "\n", "# Recompute point count AFTER downsampling\n", "n_points = points.shape[0]\n", "min_inliers = int(n_points * min_inliers_ratio)\n", "\n", "best_plane_params = None\n", "best_inliers = []\n", "max_inliers = 0\n", "\n", "start_time = time.time()\n", "\n", "for i in tqdm(range(num_iterations), desc=\"RANSAC iterations\"):\n", "    # Randomly sample 3 points\n", "    sample_indices = np.random.choice(n_points, 3, replace=False)\n", "    p1, p2, p3 = points[sample_indices]\n", "\n", "    # Compute plane normal\n", "    v1 = p2 - p1\n", "    v2 = p3 - p1\n", "    normal = np.cross(v1, v2)\n", "\n", "    norm = np.linalg.norm(normal)\n", "    if norm < 1e-6:\n", "        continue  # Skip degenerate planes\n", "\n", "    normal = normal / norm\n", "\n", "    # Enforce upward-facing normal\n", "    if normal[2] < 0:\n", "        normal = -normal\n", "\n", "    # Plane equation: ax + by + cz + d = 0\n", "    d = -np.dot(normal, p1)\n", "    plane_params = np.append(normal, d)\n", "\n", "    # Distance of all points to the plane\n", "    distances = np.abs(np.dot(points, plane_params[:3]) + d)\n", "\n", "    # Find inliers within threshold\n", "    inliers = np.where(distances < distance_threshold)[0]\n", "    n_inliers = len(inliers)\n", "\n", "    if n_inliers > max_inliers and n_inliers >= min_inliers:\n", "        best_plane_params = plane_params\n", "        best_inliers = inliers\n", "        max_inliers = n_inliers\n", "\n", "        inlier_ratio = n_inliers / n_points\n", "        if inlier_ratio > early_stop_ratio:\n", "            print(f\"Early stopping at iteration {i+1}: Found {n_inliers:,} ground points ({inlier_ratio:.2f} ratio)\")\n", "            break\n", "\n", "end_time = time.time()\n", "\n", "if best_plane_params is not None:\n", "    elapsed_time = end_time - start_time\n", "    logger.info(f\"RANSAC completed in {elapsed_time:.2f} seconds\")\n", "\n", "    ground_ratio = max_inliers / n_points\n", "    plane_eq_str = (\n", "        f\"{best_plane_params[0]:.3f}x + \"\n", "        f\"{best_plane_params[1]:.3f}y + \"\n", "        f\"{best_plane_params[2]:.3f}z + \"\n", "        f\"{best_plane_params[3]:.3f} = 0\"\n", "    )\n", "    summary_msg = f\"Ground points: {max_inliers:,} ({ground_ratio:.2f} ratio)\"\n", "    logger.info(summary_msg)\n", "    \n", "    logger.info(f\"Plane: {plane_eq_str}\")\n", "\n", "    ground_points = points[best_inliers]\n", "    nonground_points = np.delete(points, best_inliers, axis=0)\n", "else:\n", "    raise ValueError(\"RANSAC failed to find a valid ground plane.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: RANSAC Ground Detection Results\n", "\n", "The RANSAC algorithm successfully identified a ground plane with approximately 49% of points classified as ground. The plane equation shows a nearly horizontal surface (z-coefficient ≈ 1.0) with minimal slope, which is typical for flat terrain. The processing time of ~23 seconds for 1M points demonstrates RANSAC's computational efficiency. The ground ratio of 0.49 suggests a balanced distribution between ground and non-ground features, indicating the presence of significant above-ground structures."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Point Cloud Output Generation\n", "\n", "Save the segmented ground and non-ground point clouds with RANSAC-specific naming convention."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-04 15:04:19,462 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/analysis_output/trino_enel/analysis_output/trino_enel_ground.ply\n", "2025-07-04 15:04:19,466 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/analysis_output/trino_enel/analysis_output/trino_enel_nonground.ply\n", "2025-07-04 15:04:19,468 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/ransac/trino_enel_ground.ply\n", "2025-07-04 15:04:19,470 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/ransac/trino_enel_nonground.ply\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "RANSAC segmentation outputs saved:\n", "  Ground points: 14,014\n", "  Non-ground points: 22,724\n", "  Method identifier: ransac\n"]}], "source": ["# Save segmented point clouds with method-specific naming\n", "import open3d as o3d\n", "\n", "def save_ply(path, points_array, method_name=\"\"):\n", "    \"\"\"Save point cloud with method-specific naming for comparison.\"\"\"\n", "    pc = o3d.geometry.PointCloud()\n", "    pc.points = o3d.utility.Vector3dVector(points_array)\n", "    o3d.io.write_point_cloud(str(path), pc)\n", "    logger.info(f\"Saved: {path}\")\n", "\n", "# Save the point clouds to the appropriate output paths\n", "analysis_output = analysis_output_path / 'analysis_output'\n", "analysis_output.mkdir(parents=True, exist_ok=True)\n", "\n", "# Save ground and nonground points to the analysis_output directory\n", "save_ply(analysis_output / f\"{site_name}_ground.ply\", ground_points)\n", "save_ply(analysis_output / f\"{site_name}_nonground.ply\", nonground_points)\n", "\n", "# Satisfied with the results save the final output for next stage \n", "save_ply(ground_seg_path / f\"{method_name}/{site_name}_ground.ply\", ground_points)\n", "save_ply(ground_seg_path / f\"{method_name}/{site_name}_nonground.ply\", nonground_points)\n", "\n", "logger.info(f\"\\nRANSAC segmentation outputs saved:\")\n", "logger.info(f\"  Ground points: {len(ground_points):,}\")\n", "logger.info(f\"  Non-ground points: {len(nonground_points):,}\")\n", "logger.info(f\"  Method identifier: {method_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Output Generation\n", "\n", "Point clouds have been saved with RANSAC-specific naming (ransac_ground.ply, ransac_nonground.ply) to enable direct comparison with other ground segmentation methods. This naming convention ensures that results from different algorithms can be easily distinguished and analyzed side-by-side."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Statistical Analysis of Segmentation Results\n", "\n", "Calculate key metrics to evaluate RANSAC segmentation quality and characteristics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate ground to non-ground ratio\n", "# Ground/Non-Ground ratio\n", "ground_count = ground_points.shape[0]\n", "nonground_count = nonground_points.shape[0]\n", "total_points = ground_count + nonground_count\n", "\n", "ground_ratio = ground_count / total_points\n", "logger.info(f\"Ground Ratio: {ground_ratio:.4f}\")\n", "\n", "nonground_ratio = nonground_count / total_points\n", "logger.info(f\"Non-Ground Ratio: {nonground_ratio:.4f}\")\n", "\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"RANSAC Segmentation Summary:\")\n", "logger.info(\"-\" * 50)\n", "\n", "logger.info(f\"  Total points processed: {ground_points.shape[0] + nonground_points.shape[0]:,}\")\n", "logger.info(f\"  Ground points: {ground_points.shape[0]:,} ({ground_ratio:.1%})\")\n", "logger.info(f\"  Non-ground points: {nonground_points.shape[0]:,} ({1-ground_ratio:.1%})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Statistical Analysis\n", "\n", "The RANSAC method achieved a ground ratio of 49.4%, indicating a balanced segmentation between ground and non-ground features. This ratio is characteristic of areas with significant infrastructure or vegetation coverage above the ground plane."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Elevation Analysis and Vertical Separation\n", "\n", "Analyze the vertical characteristics of ground vs non-ground points to assess segmentation quality."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate elevation statistics for ground and non-ground points\n", "ground_z_mean = ground_points[:, 2].mean()\n", "ground_z_std = ground_points[:, 2].std()\n", "nonground_z_mean = nonground_points[:, 2].mean()\n", "nonground_z_std = nonground_points[:, 2].std()\n", "z_separation = nonground_z_mean - ground_z_mean\n", "\n", "logger.info(f\"  RANSAC Elevation Analysis:\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Ground elevation - Mean: {ground_z_mean:.3f}m, Std: {ground_z_std:.3f}m\")\n", "logger.info(f\"  Non-ground elevation - Mean: {nonground_z_mean:.3f}m, Std: {nonground_z_std:.3f}m\")\n", "logger.info(f\"  Vertical separation: {z_separation:.3f}m\")\n", "\n", "# Calculate elevation ranges\n", "ground_z_range = ground_points[:, 2].max() - ground_points[:, 2].min()\n", "nonground_z_range = nonground_points[:, 2].max() - nonground_points[:, 2].min()\n", "logger.info(f\"  Ground elevation range: {ground_z_range:.3f}m\")\n", "logger.info(f\"  Non-ground elevation range: {nonground_z_range:.3f}m\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Bounding Box Stats\n", "def bounding_box_stats(points):\n", "    min_bound = np.min(points, axis=0)\n", "    max_bound = np.max(points, axis=0)\n", "    return max_bound - min_bound\n", "\n", "ground_bbox = bounding_box_stats(ground_points)\n", "nonground_bbox = bounding_box_stats(nonground_points)\n", "\n", "logger.info(\"  Bounding Box Sizes (X, Y, Z):\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Ground:     {ground_bbox}\")\n", "logger.info(f\"  Non-Ground: {nonground_bbox}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Height (Z) Distribution Plot\n", "plt.figure(figsize=(10, 5))\n", "plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')\n", "plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')\n", "plt.legend()\n", "plt.title(\"Z-Height Distribution\")\n", "plt.xlabel(\"Z (Elevation)\")\n", "plt.ylabel(\"Point Count\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final readiness print\n", "logger.info(\"RANSAC Ground Segmentation - Ready!\")\n", "logger.info(\"=\" * 50)\n", "logger.info(f\"Data path: {data_path}\")\n", "logger.info(f\"Project: {project_type}/{site_name}\")\n", "logger.info(f\"Input path: {input_path}\")\n", "logger.info(f\"Output path: {ground_seg_path}\")\n", "logger.info(f\"Current run output: {current_run_path}\")\n", "logger.info(f\"RANSAC Parameters: threshold={distance_threshold}m, iterations={num_iterations}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize the results\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "# Create point cloud for ground\n", "pcd_ground = o3d.geometry.PointCloud()\n", "pcd_ground.points = o3d.utility.Vector3dVector(ground_points)\n", "pcd_ground.paint_uniform_color([0.0, 1.0, 0.0])  # Green\n", "\n", "# Create point cloud for non-ground\n", "pcd_nonground = o3d.geometry.PointCloud()\n", "pcd_nonground.points = o3d.utility.Vector3dVector(nonground_points)\n", "pcd_nonground.paint_uniform_color([1.0, 0.0, 0.0])  # Red\n", "\n", "# Show both together\n", "o3d.visualization.draw_geometries([pcd_ground, pcd_nonground],\n", "                                  window_name=\"Ground vs Non-Ground\",\n", "                                  point_show_normal=False)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}
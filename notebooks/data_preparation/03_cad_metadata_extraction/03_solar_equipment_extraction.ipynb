{"cells": [{"cell_type": "markdown", "metadata": {"tags": ["parameters"]}, "source": ["# Solar Equipment CAD Extraction\n", "\n", "Extract solar trackers and module boundaries from solar equipment CAD files.\n", "\n", "**Purpose**: Extract solar equipment data for point cloud alignment workflows  \n", "**Input**: Solar equipment CAD files (DXF format)  \n", "**Output**: Tracker coordinates and module boundaries CSV files  \n", "\n", "**Note**: This notebook handles solar equipment only.  \n", "Structural elements extraction is handled in `04_structural_site_elements_extraction.ipynb`.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 74, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters\n", "site_name = \"Castro\"\n", "project_type = \"ENEL\"\n", "target_files = [\"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\"]\n", "search_path = \"../../../data/raw\"\n", "output_dir = \"../../../output_runs/cad_metadata\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Configuration"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Solar CAD extraction started\n", "INFO:__main__:Site: Castro, Project: ENEL\n", "INFO:__main__:Target files: ['GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf']\n", "INFO:__main__:Search path: ../../../data/raw\n", "INFO:__main__:Output directory: ../../../output_runs/cad_metadata\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import ezdxf\n", "import json\n", "import logging\n", "from datetime import datetime\n", "from collections import defaultdict\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "logger.info(\"Solar CAD extraction started\")\n", "logger.info(f\"Site: {site_name}, Project: {project_type}\")\n", "logger.info(f\"Target files: {target_files}\")\n", "logger.info(f\"Search path: {search_path}\")\n", "logger.info(f\"Output directory: {output_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Coordinate System Configuration"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Loaded coordinate configuration: site_coordinate_config_20250701_170924.json\n", "INFO:__main__:Coordinate system: UTM\n", "INFO:__main__:Confidence: high\n", "INFO:__main__:Valid X range: 706094.93 to 709157.78\n", "INFO:__main__:Valid Y range: 4692702.69 to 4693259.76\n"]}], "source": ["def load_coordinate_config(config_dir=\"../../../output_runs/coordinate_analysis\"):\n", "    \"\"\"Load the most recent coordinate system configuration.\"\"\"\n", "    config_path = Path(config_dir)\n", "    \n", "    if not config_path.exists():\n", "        logger.warning(f\"Configuration directory not found: {config_path}\")\n", "        logger.warning(\"Run 01_coordinate_system_analysis.ipynb first\")\n", "        return None\n", "    \n", "    config_files = list(config_path.glob(\"*/site_coordinate_config_*.json\"))\n", "    \n", "    if not config_files:\n", "        logger.warning(f\"No coordinate configuration found in {config_path}\")\n", "        logger.warning(\"Run 01_coordinate_system_analysis.ipynb first\")\n", "        return None\n", "    \n", "    latest_config = max(config_files, key=lambda x: x.stat().st_mtime)\n", "    \n", "    try:\n", "        with open(latest_config, 'r') as f:\n", "            config = json.load(f)\n", "        \n", "        logger.info(f\"Loaded coordinate configuration: {latest_config.name}\")\n", "        logger.info(f\"Coordinate system: {config['coordinate_system']['type']}\")\n", "        logger.info(f\"Confidence: {config['coordinate_system']['confidence']}\")\n", "        \n", "        return config\n", "    \n", "    except Exception as e:\n", "        logger.error(f\"Error loading configuration: {e}\")\n", "        return None\n", "\n", "# Load configuration\n", "coord_config = load_coordinate_config()\n", "\n", "if coord_config and coord_config.get('valid_ranges'):\n", "    valid_ranges = coord_config['valid_ranges']\n", "    logger.info(f\"Valid X range: {valid_ranges['x_min']:.2f} to {valid_ranges['x_max']:.2f}\")\n", "    logger.info(f\"Valid Y range: {valid_ranges['y_min']:.2f} to {valid_ranges['y_max']:.2f}\")\n", "else:\n", "    valid_ranges = None\n", "    logger.warning(\"No valid ranges available - using basic validation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Coordinate Validation Functions"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Coordinate validator created\n"]}], "source": ["def create_coordinate_validator(valid_ranges):\n", "    \"\"\"Create coordinate validation function based on detected ranges.\"\"\"\n", "    \n", "    if not valid_ranges:\n", "        def basic_validator(x, y, z=None):\n", "            return x != 0.0 and y != 0.0 and abs(x) > 100 and abs(y) > 100\n", "        return basic_validator\n", "    \n", "    def adaptive_validator(x, y, z=None):\n", "        \"\"\"Validate coordinates using site-specific ranges.\"\"\"\n", "        if x == 0.0 and y == 0.0:\n", "            return False\n", "        if abs(x) < 100 or abs(y) < 100:\n", "            return False\n", "        if not (valid_ranges['x_min'] <= x <= valid_ranges['x_max']):\n", "            return False\n", "        if not (valid_ranges['y_min'] <= y <= valid_ranges['y_max']):\n", "            return False\n", "        return True\n", "    \n", "    return adaptive_validator\n", "\n", "# Create validator\n", "is_valid_coordinate = create_coordinate_validator(valid_ranges)\n", "logger.info(\"Coordinate validator created\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## CAD File Discovery"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Searching for CAD files...\n", "INFO:__main__:Found: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf (15.5 MB)\n", "INFO:__main__:Files to process: 1\n"]}], "source": ["def find_cad_files(search_path, target_files):\n", "    \"\"\"Find target CAD files in search path.\"\"\"\n", "    found_files = []\n", "    \n", "    for target_file in target_files:\n", "        result = !find {search_path} -name \"{target_file}\" 2>/dev/null\n", "        \n", "        if result:\n", "            file_path = Path(result[0])\n", "            file_size = file_path.stat().st_size / 1024 / 1024\n", "            \n", "            logger.info(f\"Found: {file_path.name} ({file_size:.1f} MB)\")\n", "            found_files.append(file_path)\n", "        else:\n", "            logger.warning(f\"Not found: {target_file}\")\n", "    \n", "    return found_files\n", "\n", "# Find files\n", "logger.info(\"Searching for CAD files...\")\n", "cad_files = find_cad_files(search_path, target_files)\n", "logger.info(f\"Files to process: {len(cad_files)}\")\n", "\n", "if not cad_files:\n", "    logger.error(\"No CAD files found\")\n", "    raise FileNotFoundError(\"No target CAD files found in search path\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Solar Equipment Extraction Functions"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Solar equipment extraction functions ready\n"]}], "source": ["def extract_solar_trackers(entity, layer, is_valid_coordinate):\n", "    \"\"\"Extract solar tracker coordinates from INSERT entities.\"\"\"\n", "    trackers = []\n", "    \n", "    if (entity.dxftype() == 'INSERT' and \n", "        any(keyword in layer.upper() for keyword in ['TRACKER', 'CVT'])):\n", "        \n", "        if hasattr(entity.dxf, 'insert'):\n", "            point = entity.dxf.insert\n", "            \n", "            if is_valid_coordinate(point.x, point.y, point.z):\n", "                tracker_data = {\n", "                    'x': point.x,\n", "                    'y': point.y,\n", "                    'z': point.z,\n", "                    'layer': layer,\n", "                    'block_name': getattr(entity.dxf, 'name', ''),\n", "                    'rotation': getattr(entity.dxf, 'rotation', 0.0),\n", "                    'type': 'tracker'\n", "                }\n", "                trackers.append(tracker_data)\n", "    \n", "    return trackers\n", "\n", "def extract_solar_modules(entity, layer, is_valid_coordinate):\n", "    \"\"\"Extract solar module boundaries from POLYLINE entities.\"\"\"\n", "    modules = []\n", "    \n", "    if (entity.dxftype() in ['LWPOLYLINE', 'POLYLINE'] and \n", "        any(keyword in layer.upper() for keyword in ['PVCASE', 'PV', 'MODULE'])):\n", "        \n", "        try:\n", "            bbox = entity.bbox()\n", "            if bbox:\n", "                center_x = (bbox[0].x + bbox[1].x) / 2\n", "                center_y = (bbox[0].y + bbox[1].y) / 2\n", "                center_z = (bbox[0].z + bbox[1].z) / 2\n", "                \n", "                if is_valid_coordinate(center_x, center_y, center_z):\n", "                    module_data = {\n", "                        'x': center_x,\n", "                        'y': center_y,\n", "                        'z': center_z,\n", "                        'layer': layer,\n", "                        'x_min': bbox[0].x,\n", "                        'y_min': bbox[0].y,\n", "                        'x_max': bbox[1].x,\n", "                        'y_max': bbox[1].y,\n", "                        'width': bbox[1].x - bbox[0].x,\n", "                        'height': bbox[1].y - bbox[0].y,\n", "                        'type': 'module'\n", "                    }\n", "                    modules.append(module_data)\n", "        except:\n", "            pass\n", "    \n", "    return modules\n", "\n", "logger.info(\"Solar equipment extraction functions ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main CAD Processing"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Starting CAD file processing...\n", "INFO:__main__:Processing: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n", "INFO:__main__:Extracted 508 trackers and 0 modules\n", "INFO:__main__:Processing complete: 508 total trackers, 0 total modules\n"]}], "source": ["def process_cad_file(file_path, is_valid_coordinate):\n", "    \"\"\"Process a single CAD file and extract solar equipment data.\"\"\"\n", "    logger.info(f\"Processing: {file_path.name}\")\n", "    \n", "    try:\n", "        doc = ezdxf.readfile(file_path)\n", "        modelspace = doc.modelspace()\n", "        \n", "        all_trackers = []\n", "        all_modules = []\n", "        entity_stats = defaultdict(int)\n", "        \n", "        for entity in modelspace:\n", "            layer = getattr(entity.dxf, 'layer', 'UNKNOWN')\n", "            entity_type = entity.dxftype()\n", "            entity_stats[f\"{entity_type}_{layer}\"] += 1\n", "            \n", "            # Extract trackers\n", "            trackers = extract_solar_trackers(entity, layer, is_valid_coordinate)\n", "            for tracker in trackers:\n", "                tracker['source_file'] = file_path.name\n", "            all_trackers.extend(trackers)\n", "            \n", "            # Extract modules\n", "            modules = extract_solar_modules(entity, layer, is_valid_coordinate)\n", "            for module in modules:\n", "                module['source_file'] = file_path.name\n", "            all_modules.extend(modules)\n", "        \n", "        logger.info(f\"Extracted {len(all_trackers)} trackers and {len(all_modules)} modules\")\n", "        \n", "        return all_trackers, all_modules, dict(entity_stats)\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error processing {file_path.name}: {e}\")\n", "        return [], [], {}\n", "\n", "# Process all CAD files\n", "logger.info(\"Starting CAD file processing...\")\n", "\n", "all_trackers = []\n", "all_modules = []\n", "processing_stats = {}\n", "\n", "for file_path in cad_files:\n", "    if file_path.suffix.lower() == '.dxf':\n", "        trackers, modules, entity_stats = process_cad_file(file_path, is_valid_coordinate)\n", "        \n", "        all_trackers.extend(trackers)\n", "        all_modules.extend(modules)\n", "        \n", "        processing_stats[file_path.name] = {\n", "            'tracker_count': len(trackers),\n", "            'module_count': len(modules),\n", "            'entity_types': entity_stats\n", "        }\n", "    else:\n", "        logger.warning(f\"Skipping {file_path.name} (DWG format - convert to DXF first)\")\n", "\n", "logger.info(f\"Processing complete: {len(all_trackers)} total trackers, {len(all_modules)} total modules\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Quality Analysis"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Tracker spatial extent: 1189.6 x 410.4 m\n", "INFO:__main__:Data quality analysis complete\n"]}], "source": ["def analyze_extraction_quality(trackers, modules, coord_config):\n", "    \"\"\"Analyze the quality of extracted data.\"\"\"\n", "    analysis = {\n", "        'extraction_timestamp': datetime.now().isoformat(),\n", "        'coordinate_system_used': coord_config['coordinate_system']['type'] if coord_config else 'Unknown',\n", "        'validation_applied': coord_config is not None\n", "    }\n", "    \n", "    if trackers:\n", "        tracker_coords = np.array([[t['x'], t['y']] for t in trackers])\n", "        analysis['tracker_analysis'] = {\n", "            'count': len(trackers),\n", "            'x_range': [float(tracker_coords[:, 0].min()), float(tracker_coords[:, 0].max())],\n", "            'y_range': [float(tracker_coords[:, 1].min()), float(tracker_coords[:, 1].max())],\n", "            'spatial_extent': {\n", "                'width': float(tracker_coords[:, 0].max() - tracker_coords[:, 0].min()),\n", "                'height': float(tracker_coords[:, 1].max() - tracker_coords[:, 1].min())\n", "            }\n", "        }\n", "        \n", "        logger.info(f\"Tracker spatial extent: {analysis['tracker_analysis']['spatial_extent']['width']:.1f} x {analysis['tracker_analysis']['spatial_extent']['height']:.1f} m\")\n", "    \n", "    if modules:\n", "        module_coords = np.array([[m['x'], m['y']] for m in modules])\n", "        analysis['module_analysis'] = {\n", "            'count': len(modules),\n", "            'x_range': [float(module_coords[:, 0].min()), float(module_coords[:, 0].max())],\n", "            'y_range': [float(module_coords[:, 1].min()), float(module_coords[:, 1].max())],\n", "            'average_dimensions': {\n", "                'width': float(np.mean([m['width'] for m in modules if 'width' in m])),\n", "                'height': float(np.mean([m['height'] for m in modules if 'height' in m]))\n", "            } if any('width' in m for m in modules) else None\n", "        }\n", "    \n", "    return analysis\n", "\n", "# Analyze extraction quality\n", "if all_trackers or all_modules:\n", "    quality_analysis = analyze_extraction_quality(all_trackers, all_modules, coord_config)\n", "    logger.info(\"Data quality analysis complete\")\n", "else:\n", "    logger.warning(\"No data extracted for quality analysis\")\n", "    quality_analysis = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Output Generation"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Saving results to: ../../../output_runs/cad_metadata/castro_solar_extraction_20250701_171433\n", "INFO:__main__:Saved tracker coordinates: tracker_coordinates_20250701_171433.csv (508 points)\n", "INFO:__main__:Saved extraction summary: extraction_summary_20250701_171433.json\n", "INFO:__main__:Solar CAD extraction completed successfully\n", "INFO:__main__:Output directory: ../../../output_runs/cad_metadata/castro_solar_extraction_20250701_171433\n", "INFO:__main__:Files generated: ['tracker_coordinates_20250701_171433.csv']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Extraction Summary:\n", "Site: Castro\n", "Trackers extracted: 508\n", "Modules extracted: 0\n", "Coordinate system: UTM\n", "Output directory: ../../../output_runs/cad_metadata/castro_solar_extraction_20250701_171433\n"]}], "source": ["def save_extraction_results(trackers, modules, quality_analysis, processing_stats, \n", "                          site_name, output_dir):\n", "    \"\"\"Save extraction results to CSV files and generate summary.\"\"\"\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    output_path = Path(output_dir)\n", "    run_output_dir = output_path / f\"{site_name.lower()}_solar_extraction_{timestamp}\"\n", "    run_output_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    logger.info(f\"Saving results to: {run_output_dir}\")\n", "    \n", "    saved_files = []\n", "    \n", "    # Save tracker coordinates\n", "    if trackers:\n", "        trackers_df = pd.DataFrame(trackers)\n", "        tracker_file = run_output_dir / f\"tracker_coordinates_{timestamp}.csv\"\n", "        trackers_df.to_csv(tracker_file, index=False)\n", "        saved_files.append(tracker_file.name)\n", "        logger.info(f\"Saved tracker coordinates: {tracker_file.name} ({len(trackers)} points)\")\n", "    \n", "    # Save module boundaries\n", "    if modules:\n", "        modules_df = pd.DataFrame(modules)\n", "        module_file = run_output_dir / f\"module_boundaries_{timestamp}.csv\"\n", "        modules_df.to_csv(module_file, index=False)\n", "        saved_files.append(module_file.name)\n", "        logger.info(f\"Saved module boundaries: {module_file.name} ({len(modules)} areas)\")\n", "    \n", "    # Save comprehensive summary\n", "    summary = {\n", "        'extraction_info': {\n", "            'site_name': site_name,\n", "            'timestamp': timestamp,\n", "            'target_files': target_files,\n", "            'files_processed': list(processing_stats.keys())\n", "        },\n", "        'results': {\n", "            'tracker_count': len(trackers),\n", "            'module_count': len(modules),\n", "            'files_generated': saved_files\n", "        },\n", "        'processing_stats': processing_stats,\n", "        'quality_analysis': quality_analysis,\n", "        'coordinate_config_used': coord_config['analysis_info']['timestamp'] if coord_config else None,\n", "        'usage_instructions': {\n", "            'tracker_coordinates': 'Primary alignment points for point cloud registration',\n", "            'module_boundaries': 'Secondary validation areas for spatial coverage checks',\n", "            'coordinate_system': quality_analysis['coordinate_system_used'] if quality_analysis else 'Unknown'\n", "        }\n", "    }\n", "    \n", "    summary_file = run_output_dir / f\"extraction_summary_{timestamp}.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(summary, f, indent=2, default=str)\n", "    \n", "    logger.info(f\"Saved extraction summary: {summary_file.name}\")\n", "    \n", "    return run_output_dir, saved_files\n", "\n", "# Save results\n", "if all_trackers or all_modules:\n", "    output_directory, output_files = save_extraction_results(\n", "        all_trackers, all_modules, quality_analysis, processing_stats, \n", "        site_name, output_dir\n", "    )\n", "    \n", "    logger.info(\"Solar CAD extraction completed successfully\")\n", "    logger.info(f\"Output directory: {output_directory}\")\n", "    logger.info(f\"Files generated: {output_files}\")\n", "    \n", "    # Final summary\n", "    print(f\"\\nExtraction Summary:\")\n", "    print(f\"Site: {site_name}\")\n", "    print(f\"Trackers extracted: {len(all_trackers)}\")\n", "    print(f\"Modules extracted: {len(all_modules)}\")\n", "    print(f\"Coordinate system: {quality_analysis['coordinate_system_used'] if quality_analysis else 'Unknown'}\")\n", "    print(f\"Output directory: {output_directory}\")\n", "    \n", "else:\n", "    logger.error(\"No data extracted - check CAD files and layer names\")\n", "    raise ValueError(\"No solar equipment data extracted from CAD files\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}
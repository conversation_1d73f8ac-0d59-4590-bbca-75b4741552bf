{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC Structure Exploration and Understanding\n", "\n", "## Overview\n", "\n", "This notebook provides an exploratory analysis of IFC file structure to understand:\n", "- File organization and schema\n", "- Available element types and their properties\n", "- Coordinate systems and spatial relationships\n", "- Data quality and completeness\n", "- Optimal extraction strategies\n", "\n", "**Purpose**: Understand IFC structure before implementing systematic metadata extraction\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters\n", "ifc_file_path = \"/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc\"\n", "output_dir = \"../../data/processed/trino_enel/ifc_exploration\"\n", "\n", "# Exploration parameters\n", "max_elements_to_analyze = 10  # Limit for detailed analysis\n", "show_detailed_properties = True\n", "analyze_coordinate_systems = True"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IFC STRUCTURE EXPLORATION\n", "Analyzing: GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "Output: ../../data/processed/trino_enel/ifc_exploration\n"]}], "source": ["import ifcopenshell\n", "import ifcopenshell.geom\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "import json\n", "from collections import defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure pandas for better display in exploration\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 30)\n", "pd.set_option('display.precision', 3)\n", "\n", "# Setup output directory\n", "output_path = Path(output_dir)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"IFC STRUCTURE EXPLORATION\")\n", "print(f\"Analyzing: {Path(ifc_file_path).name}\")\n", "print(f\"Output: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Basic IFC File Analysis"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading IFC file...\n", "\n", "=== BASIC IFC INFORMATION ===\n", "Schema: IFC4\n", "Total entities: 2212652\n", "\n", "=== PROJECT HIERARCHY ===\n", "Projects: 1\n", "  Name: Project Number\n", "  Description: None\n", "  GlobalId: 1RIB0EUg56uRQ_2YrLyc3N\n", "Sites: 1\n", "  Name: Surface:840048\n", "  Description: None\n", "  GlobalId: 1RIB0EUg56uRQ_2YrLyc3L\n", "Buildings: 1\n", "Building Storeys: 2\n"]}], "source": ["# Load IFC file\n", "print(\"Loading IFC file...\")\n", "ifc_file = ifcopenshell.open(ifc_file_path)\n", "\n", "print(f\"\\n=== BASIC IFC INFORMATION ===\")\n", "print(f\"Schema: {ifc_file.schema}\")\n", "print(f\"Total entities: {len(list(ifc_file))}\")\n", "\n", "# Get project hierarchy\n", "projects = ifc_file.by_type('IfcProject')\n", "sites = ifc_file.by_type('IfcSite')\n", "buildings = ifc_file.by_type('IfcBuilding')\n", "building_storeys = ifc_file.by_type('IfcBuildingStorey')\n", "\n", "print(f\"\\n=== PROJECT HIERARCHY ===\")\n", "print(f\"Projects: {len(projects)}\")\n", "if projects:\n", "    project = projects[0]\n", "    print(f\"  Name: {getattr(project, 'Name', 'Unnamed')}\")\n", "    print(f\"  Description: {getattr(project, 'Description', 'No description')}\")\n", "    print(f\"  GlobalId: {project.GlobalId}\")\n", "\n", "print(f\"Sites: {len(sites)}\")\n", "if sites:\n", "    site = sites[0]\n", "    print(f\"  Name: {getattr(site, 'Name', 'Unnamed')}\")\n", "    print(f\"  Description: {getattr(site, 'Description', 'No description')}\")\n", "    print(f\"  GlobalId: {site.GlobalId}\")\n", "\n", "print(f\"Buildings: {len(buildings)}\")\n", "print(f\"Building Storeys: {len(building_storeys)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Discover All Element Types"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ELEMENT TYPE DISCOVERY ===\n", "\n", "BUILDING ELEMENTS (1 types):\n", "  IfcColumn: 14460\n", "\n", "SPATIAL ELEMENTS (3 types):\n", "  IfcBuildingStorey: 2\n", "  IfcBuilding: 1\n", "  IfcSite: 1\n", "\n", "RELATIONSHIP ELEMENTS (7 types):\n", "  IfcRelDefinesByProperties: 279463\n", "  IfcRelNests: 26145\n", "  IfcRelConnectsPorts: 13410\n", "  IfcRelDefinesByType: 1461\n", "  IfcRelAssociatesMaterial: 8\n", "  IfcRelAggregates: 3\n", "  IfcRelContainedInSpatialStructure: 2\n", "\n", "OTHER ELEMENTS (59 types):\n", "  IfcPropertySingleValue: 687868\n", "  IfcIndexedPolygonalFace: 318849\n", "  IfcPropertySet: 279465\n", "  IfcAxis2Placement3D: 109093\n", "  IfcCartesianPoint: 99058\n", "  IfcLocalPlacement: 91933\n", "  IfcDirection: 63313\n", "  IfcDistributionPort: 51324\n", "  IfcShapeRepresentation: 42064\n", "  IfcProductDefinitionShape: 40606\n", "  ... and 49 more element types\n"]}], "source": ["# Discover all element types in the file\n", "print(\"\\n=== ELEMENT TYPE DISCOVERY ===\")\n", "\n", "# Count all entity types\n", "entity_counts = defaultdict(int)\n", "for entity in ifc_file:\n", "    entity_counts[entity.is_a()] += 1\n", "\n", "# Filter for building elements (typically start with 'Ifc' and are physical)\n", "building_elements = {}\n", "spatial_elements = {}\n", "relationship_elements = {}\n", "other_elements = {}\n", "\n", "for entity_type, count in sorted(entity_counts.items()):\n", "    if entity_type.startswith('IfcRel'):\n", "        relationship_elements[entity_type] = count\n", "    elif entity_type in ['IfcSite', 'IfcBuilding', 'IfcBuildingStorey', 'IfcSpace']:\n", "        spatial_elements[entity_type] = count\n", "    elif entity_type in ['IfcWall', 'IfcSlab', 'IfcBeam', 'IfcColumn', 'IfcPile', 'IfcRoof', 'IfcStair', 'IfcWindow', 'IfcDoor']:\n", "        building_elements[entity_type] = count\n", "    else:\n", "        other_elements[entity_type] = count\n", "\n", "print(f\"\\nBUILDING ELEMENTS ({len(building_elements)} types):\")\n", "for elem_type, count in sorted(building_elements.items(), key=lambda x: x[1], reverse=True):\n", "    print(f\"  {elem_type}: {count}\")\n", "\n", "print(f\"\\nSPATIAL ELEMENTS ({len(spatial_elements)} types):\")\n", "for elem_type, count in sorted(spatial_elements.items(), key=lambda x: x[1], reverse=True):\n", "    print(f\"  {elem_type}: {count}\")\n", "\n", "print(f\"\\nRELATIONSHIP ELEMENTS ({len(relationship_elements)} types):\")\n", "for elem_type, count in sorted(relationship_elements.items(), key=lambda x: x[1], reverse=True)[:10]:  # Top 10\n", "    print(f\"  {elem_type}: {count}\")\n", "if len(relationship_elements) > 10:\n", "    print(f\"  ... and {len(relationship_elements) - 10} more relationship types\")\n", "\n", "print(f\"\\nOTHER ELEMENTS ({len(other_elements)} types):\")\n", "for elem_type, count in sorted(other_elements.items(), key=lambda x: x[1], reverse=True)[:10]:  # Top 10\n", "    print(f\"  {elem_type}: {count}\")\n", "if len(other_elements) > 10:\n", "    print(f\"  ... and {len(other_elements) - 10} more element types\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Deep Dive into Key Element Types"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== DETAILED ELEMENT ANALYSIS ===\n", "\n", "IfcPile: Not found\n", "\n", "IfcBeam: Not found\n", "\n", "IfcColumn: 14460 elements\n", "Analyzing first 10 elements...\n", "\n", "  Element 1:\n", "    GlobalId: 1u7AZf3On2lwsljDdawZWm\n", "    Name: TRPL_Tracker Pile:TRPL_Tracker Pile:952577\n", "    Description: None\n", "    Tag: 952577\n", "    Geometry extraction failed: 'tuple' object has no attribute 'data'...\n", "    Has Geometry: True\n", "    Materials: ['Tracker']\n", "    Property Sets: ['Pset_ColumnCommon', 'Pset_EnvironmentalImpactIndicators', 'Pset_ReinforcementBarPitchOfColumn', 'BillOfQuantityInformation', 'ClassificationSystemAssetBS', 'ClassificationSystemSystemBS', 'CommonInformation']\n", "\n", "  Element 2:\n", "    GlobalId: 1u7AZf3On2lwsljDdawZWp\n", "    Name: TRPL_Tracker Pile:TRPL_Tracker Pile:952578\n", "    Description: None\n", "    Tag: 952578\n", "    Geometry extraction failed: 'tuple' object has no attribute 'data'...\n", "    Has Geometry: True\n", "    Materials: ['Tracker']\n", "    Property Sets: ['Pset_ColumnCommon', 'Pset_EnvironmentalImpactIndicators', 'Pset_ReinforcementBarPitchOfColumn', 'BillOfQuantityInformation', 'ClassificationSystemAssetBS', 'ClassificationSystemSystemBS', 'CommonInformation']\n", "\n", "  Element 3:\n", "    GlobalId: 1u7AZf3On2lwsljDdawZWo\n", "    Name: TRPL_Tracker Pile:TRPL_Tracker Pile:952579\n", "    Description: None\n", "    Tag: 952579\n", "    Geometry extraction failed: 'tuple' object has no attribute 'data'...\n", "    Has Geometry: True\n", "    Materials: ['Tracker']\n", "    Property Sets: ['Pset_ColumnCommon', 'Pset_EnvironmentalImpactIndicators', 'Pset_ReinforcementBarPitchOfColumn', 'BillOfQuantityInformation', 'ClassificationSystemAssetBS', 'ClassificationSystemSystemBS', 'CommonInformation']\n", "  ... and 14450 more IfcColumn elements\n", "\n", "IfcSlab: Not found\n", "\n", "IfcWall: Not found\n"]}], "source": ["# Analyze key building elements in detail\n", "key_elements = ['IfcPile', 'IfcBeam', 'IfcColumn', 'IfcSlab', 'IfcWall']\n", "\n", "print(\"\\n=== DETAILED ELEMENT ANALYSIS ===\")\n", "\n", "for element_type in key_elements:\n", "    elements = ifc_file.by_type(element_type)\n", "    if not elements:\n", "        print(f\"\\n{element_type}: Not found\")\n", "        continue\n", "    \n", "    print(f\"\\n{element_type}: {len(elements)} elements\")\n", "    \n", "    # Analyze first few elements\n", "    sample_size = min(max_elements_to_analyze, len(elements))\n", "    print(f\"Analyzing first {sample_size} elements...\")\n", "    \n", "    for i, element in enumerate(elements[:sample_size]):\n", "        print(f\"\\n  Element {i+1}:\")\n", "        print(f\"    GlobalId: {element.GlobalId}\")\n", "        print(f\"    Name: {getattr(element, 'Name', 'Unnamed')}\")\n", "        print(f\"    Description: {getattr(element, 'Description', 'No description')}\")\n", "        print(f\"    Tag: {getattr(element, 'Tag', 'No tag')}\")\n", "        \n", "        # Check for geometry\n", "        has_geometry = False\n", "        if hasattr(element, 'ObjectPlacement') and element.ObjectPlacement:\n", "            try:\n", "                settings = ifcopenshell.geom.settings()\n", "                shape = ifcopenshell.geom.create_shape(settings, element)\n", "                if shape:\n", "                    has_geometry = True\n", "                    # Get transformation matrix\n", "                    matrix = shape.transformation.matrix.data\n", "                    transformation_matrix = np.array(matrix).reshape(4, 4)\n", "                    position = transformation_matrix[:3, 3]\n", "                    print(f\"    Position: [{position[0]:.2f}, {position[1]:.2f}, {position[2]:.2f}]\")\n", "                    \n", "                    # Get geometry info\n", "                    vertices = shape.geometry.verts\n", "                    if vertices:\n", "                        vertices_array = np.array(vertices).reshape(-1, 3)\n", "                        bbox_min = np.min(vertices_array, axis=0)\n", "                        bbox_max = np.max(vertices_array, axis=0)\n", "                        dimensions = bbox_max - bbox_min\n", "                        print(f\"    Dimensions: [{dimensions[0]:.2f}, {dimensions[1]:.2f}, {dimensions[2]:.2f}]\")\n", "            except Exception as e:\n", "                print(f\"    Geometry extraction failed: {str(e)[:50]}...\")\n", "        \n", "        print(f\"    Has Geometry: {has_geometry}\")\n", "        \n", "        # Check for materials\n", "        materials = []\n", "        if hasattr(element, 'HasAssociations'):\n", "            for association in element.HasAssociations:\n", "                if association.is_a('IfcRelAssociatesMaterial'):\n", "                    material = association.RelatingMaterial\n", "                    if hasattr(material, 'Name'):\n", "                        materials.append(material.Name)\n", "        print(f\"    Materials: {materials if materials else 'None'}\")\n", "        \n", "        # Check for property sets\n", "        property_sets = []\n", "        if hasattr(element, 'IsDefinedBy'):\n", "            for definition in element.IsDefinedBy:\n", "                if definition.is_a('IfcRelDefinesByProperties'):\n", "                    property_set = definition.RelatingPropertyDefinition\n", "                    if property_set.is_a('IfcPropertySet'):\n", "                        property_sets.append(property_set.Name)\n", "        print(f\"    Property Sets: {property_sets if property_sets else 'None'}\")\n", "        \n", "        if i >= 2:  # Limit detailed output\n", "            break\n", "    \n", "    if len(elements) > sample_size:\n", "        print(f\"  ... and {len(elements) - sample_size} more {element_type} elements\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Coordinate System Analysis"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== COORDINATE SYSTEM ANALYSIS ===\n", "Map Conversions: 0\n", "\n", "Projected CRS: 0\n", "\n", "Geometric Contexts: 5\n", "  Context 1:\n", "    ContextType: Model\n", "    CoordinateSpaceDimension: 3\n", "    Precision: 1e-05\n", "    World Coordinate System: IfcAxis2Placement3D\n", "  Context 2:\n", "    ContextType: Model\n", "    CoordinateSpaceDimension: 3\n", "    Precision: 1e-05\n", "    World Coordinate System: IfcAxis2Placement3D\n", "  Context 3:\n", "    ContextType: Model\n", "    CoordinateSpaceDimension: 3\n", "    Precision: 1e-05\n", "    World Coordinate System: IfcAxis2Placement3D\n", "  Context 4:\n", "    ContextType: Model\n", "    CoordinateSpaceDimension: 3\n", "    Precision: 1e-05\n", "    World Coordinate System: IfcAxis2Placement3D\n", "  Context 5:\n", "    ContextType: Model\n", "    CoordinateSpaceDimension: 3\n", "    Precision: 1e-05\n", "    World Coordinate System: IfcAxis2Placement3D\n"]}], "source": ["# Analyze coordinate systems and spatial references\n", "print(\"\\n=== COORDINATE SYSTEM ANALYSIS ===\")\n", "\n", "# Look for coordinate reference systems\n", "map_conversions = ifc_file.by_type('IfcMapConversion')\n", "projected_crs = ifc_file.by_type('IfcProjectedCRS')\n", "geometric_contexts = ifc_file.by_type('IfcGeometricRepresentationContext')\n", "\n", "print(f\"Map Conversions: {len(map_conversions)}\")\n", "if map_conversions:\n", "    for i, conversion in enumerate(map_conversions):\n", "        print(f\"  Conversion {i+1}:\")\n", "        print(f\"    Eastings: {getattr(conversion, 'Eastings', 'Not specified')}\")\n", "        print(f\"    Northings: {getattr(conversion, 'Northings', 'Not specified')}\")\n", "        print(f\"    OrthogonalHeight: {getattr(conversion, 'OrthogonalHeight', 'Not specified')}\")\n", "        print(f\"    XAxisAbscissa: {getattr(conversion, 'XAxisAbscissa', 'Not specified')}\")\n", "        print(f\"    XAxisOrdinate: {getattr(conversion, 'XAxisOrdinate', 'Not specified')}\")\n", "        print(f\"    Scale: {getattr(conversion, 'Scale', 'Not specified')}\")\n", "\n", "print(f\"\\nProjected CRS: {len(projected_crs)}\")\n", "if projected_crs:\n", "    for i, crs in enumerate(projected_crs):\n", "        print(f\"  CRS {i+1}:\")\n", "        print(f\"    Name: {getattr(crs, 'Name', 'Unnamed')}\")\n", "        print(f\"    Description: {getattr(crs, 'Description', 'No description')}\")\n", "        print(f\"    GeodeticDatum: {getattr(crs, 'GeodeticDatum', 'Not specified')}\")\n", "        print(f\"    MapProjection: {getattr(crs, 'MapProjection', 'Not specified')}\")\n", "\n", "print(f\"\\nGeometric Contexts: {len(geometric_contexts)}\")\n", "if geometric_contexts:\n", "    for i, context in enumerate(geometric_contexts):\n", "        print(f\"  Context {i+1}:\")\n", "        print(f\"    ContextType: {getattr(context, 'ContextType', 'Not specified')}\")\n", "        print(f\"    CoordinateSpaceDimension: {getattr(context, 'CoordinateSpaceDimension', 'Not specified')}\")\n", "        print(f\"    Precision: {getattr(context, 'Precision', 'Not specified')}\")\n", "        if hasattr(context, 'WorldCoordinateSystem') and context.WorldCoordinateSystem:\n", "            wcs = context.WorldCoordinateSystem\n", "            print(f\"    World Coordinate System: {wcs.is_a()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Property Set Analysis"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== PROPERTY SET ANALYSIS ===\n", "Total Property Sets: 279465\n", "\n", "Property Set Summary:\n", "\n", "  Pset_EnvironmentalImpactIndicators: 40605 instances\n", "    Properties (1): Reference\n", "    Sample values:\n", "      Reference: STRI_PV String 2x28\n", "\n", "  ClassificationSystemAssetBS: 37784 instances\n", "    Properties (4): Cluster_Txt, FunctionalGroup_Txt, PlantArea_Txt, SubArea_Txt\n", "    Sample values:\n", "      PlantArea_Txt: PVP\n", "      SubArea_Txt: SA03\n", "      Cluster_Txt: CL01\n", "      FunctionalGroup_Txt: SF03\n", "\n", "  ClassificationSystemSystemBS: 34815 instances\n", "    Properties (2): MainSystem_Txt, SystemType_Txt\n", "    Sample values:\n", "      MainSystem_Txt: ELE\n", "      SystemType_Txt: LV\n", "\n", "  CommonInformation: 33904 instances\n", "    Properties (6): ManufacturerName_Txt, ManufacturerSerialNumber_Txt, Model_Txt, OtherTagging_Txt, Status_Txt, Type_Txt\n", "    Sample values:\n", "      ManufacturerName_Txt: NA\n", "      Model_Txt: NA\n", "      ManufacturerSerialNumber_Txt: NA\n", "      OtherTagging_Txt: TBD\n", "      Type_Txt: TBD\n", "      ... and 1 more properties\n", "\n", "  CommonDesignAndTechnicalData: 22999 instances\n", "    Properties (1): EleTagNo_Txt\n", "    Sample values:\n", "      EleTagNo_Txt: MA0103.MQA11.XD015-WF002\n", "\n", "  BillOfQuantityInformation: 21575 instances\n", "    Properties (12): ActivityDescription1_Txt, ActivityDescription2_Txt, ActivityDescription3_Txt, Item1_Txt, Item2_Txt, Item3_Txt, NetQuantity1_Integer, NetQuantity2_Integer, NetQuantity3_Integer, UnitOfMeasure1_Txt, UnitOfMeasure2_Txt, UnitOfMeasure3_Txt\n", "    Sample values:\n", "      Item1_Txt: TBD\n", "      ActivityDescription1_Txt: TBD\n", "      NetQuantity1_Integer: 999\n", "      Item2_Txt: TBD\n", "      ActivityDescription2_Txt: TBD\n", "      ... and 7 more properties\n", "\n", "  Pset_ColumnCommon: 14461 instances\n", "    Properties (2): IsEx<PERSON>al, Reference\n", "    Sample values:\n", "      Reference: TRPL_Tracker <PERSON>\n", "      IsExternal: False\n", "\n", "  Pset_ReinforcementBarPitchOfColumn: 14460 instances\n", "    Properties (1): Reference\n", "    Sample values:\n", "      Reference: TRPL_Tracker <PERSON>\n", "\n", "  CommonDimension: 12797 instances\n", "    Properties (2): Length_m, SolarCablesLength_m\n", "    Sample values:\n", "      SolarCablesLength_m: 94.0\n", "      Length_m: 0.957459999999992\n", "\n", "  Pset_CableCarrierSegmentTypeCommon: 7115 instances\n", "    Properties (1): Reference\n", "    Sample values:\n", "      Reference: DCC_LV DC Cable\n", "\n", "  CableDesignAndTechnicalData: 7115 instances\n", "    Properties (3): ConnectionType_Txt, FromID_Txt, ToID_Txt\n", "    Sample values:\n", "      ConnectionType_Txt: DC\n", "      FromID_Txt: MA0103.MQA11.XD014\n", "      ToID_Txt: MA0103.MSE01\n", "\n", "  CableDimension: 7115 instances\n", "    Properties (1): CableTypeDiameter_m\n", "    Sample values:\n", "      CableTypeDiameter_m: 0.015999999999999997\n", "\n", "  Pset_CableCarrierFittingTypeCommon: 6700 instances\n", "    Properties (1): Reference\n", "    Sample values:\n", "      Reference: DCC_LV DC Fittings\n", "\n", "  Pset_ElectricMotorTypeCommon: 6192 instances\n", "    Properties (1): Reference\n", "    Sample values:\n", "      Reference: TRK_Tracker 2x28\n", "\n", "  Pset_SolarDeviceTypeCommon: 5682 instances\n", "    Properties (1): Reference\n", "    Sample values:\n", "      Reference: STRI_PV String 2x28\n", "\n", "  CircuitingDesignAndTechnicalData: 5682 instances\n", "    Properties (1): NumberOfPVmodules_Integer\n", "    Sample values:\n", "      NumberOfPVmodules_Integer: 28\n", "\n", "  Pset_ElectricDistributionBoardTypeCommon: 404 instances\n", "    Properties (1): Reference\n", "    Sample values:\n", "      Reference: MVS_MV Switchgear 1.42x0.92x1.5\n", "\n", "  Pset_TransformerTypeCommon: 51 instances\n", "    Properties (1): Reference\n", "    Sample values:\n", "      Reference: PCU_Conversion Unit 3.0x2.4x2.75\n", "\n", "  Pset_BuildingElementProxyCommon: 3 instances\n", "    Properties (2): IsEx<PERSON>al, Reference\n", "    Sample values:\n", "      IsExternal: False\n", "      Reference: SSE_Electrical Substation\n", "\n", "  Pset_BuildingStoreyCommon: 3 instances\n", "    Properties (2): AboveGround, Reference\n", "    Sample values:\n", "      Reference: Circle Head - <PERSON> Datum\n", "      AboveGround: False\n", "\n", "  Pset_SiteCommon: 1 instances\n", "    Properties (1): Reference\n", "    Sample values:\n", "      Reference: Surface\n", "\n", "  Pset_BuildingCommon: 1 instances\n", "    Properties (3): IsLandmarked, NumberOfStoreys, Reference\n", "    Sample values:\n", "      Reference: Project Information\n", "      NumberOfStoreys: 2\n", "      IsLandmarked: False\n", "\n", "  Pset_BuildingSystemCommon: 1 instances\n", "    Properties (1): Reference\n", "    Sample values:\n", "      Reference: Project Information\n"]}], "source": ["# Analyze property sets across the model\n", "print(\"\\n=== PROPERTY SET ANALYSIS ===\")\n", "\n", "property_sets = ifc_file.by_type('IfcPropertySet')\n", "print(f\"Total Property Sets: {len(property_sets)}\")\n", "\n", "# Collect all property set names and their properties\n", "pset_analysis = defaultdict(lambda: {'count': 0, 'properties': set(), 'sample_values': {}})\n", "\n", "for pset in property_sets:\n", "    pset_name = pset.Name\n", "    pset_analysis[pset_name]['count'] += 1\n", "    \n", "    if hasattr(pset, 'HasProperties'):\n", "        for prop in pset.HasProperties:\n", "            if prop.is_a('IfcPropertySingleValue'):\n", "                prop_name = prop.Name\n", "                pset_analysis[pset_name]['properties'].add(prop_name)\n", "                \n", "                # Store sample value\n", "                if prop_name not in pset_analysis[pset_name]['sample_values']:\n", "                    prop_value = prop.NominalValue.wrappedValue if prop.NominalValue else None\n", "                    pset_analysis[pset_name]['sample_values'][prop_name] = prop_value\n", "\n", "# Display property set analysis\n", "print(f\"\\nProperty Set Summary:\")\n", "for pset_name, info in sorted(pset_analysis.items(), key=lambda x: x[1]['count'], reverse=True):\n", "    print(f\"\\n  {pset_name}: {info['count']} instances\")\n", "    print(f\"    Properties ({len(info['properties'])}): {', '.join(sorted(info['properties']))}\")\n", "    \n", "    if show_detailed_properties and info['sample_values']:\n", "        print(f\"    Sample values:\")\n", "        for prop_name, value in list(info['sample_values'].items())[:5]:  # Show first 5\n", "            print(f\"      {prop_name}: {value}\")\n", "        if len(info['sample_values']) > 5:\n", "            print(f\"      ... and {len(info['sample_values']) - 5} more properties\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Spatial Relationships and Hierarchy"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SPATIAL HIERARCHY ANALYSIS ===\n", "Spatial Containment Relations: 2\n", "Aggregation Relations: 3\n", "\n", "Spatial Hierarchy:\n", "\n", "  IfcBuildingStorey: Level 0 contains 14944 elements:\n", "    IfcBuildingElementProxy: 1\n", "    IfcCableCarrierFitting: 6700\n", "    IfcCableCarrierSegment: 7115\n", "    IfcElectricDistributionBoard: 404\n", "    IfcSolarDevice: 673\n", "    IfcTransformer: 51\n", "      - IfcSolarDevice: STRI_PV String:STRI_PV String 2x28, 0 V, Single Phase, 2 Wires:770747\n", "      - IfcSolarDevice: STRI_PV String:STRI_PV String 2x28, 0 V, Single Phase, 2 Wires:770890\n", "      - IfcSolarDevice: STRI_PV String:STRI_PV String 2x14, 0 V, Single Phase, 2 Wires:770919\n", "      - IfcSolarDevice: STRI_PV String:STRI_PV String 2x28, 0 V, Single Phase, 2 Wires:771468\n", "      - IfcSolarDevice: STRI_PV String:STRI_PV String 2x28, 0 V, Single Phase, 2 Wires:771469\n", "      ... and 14939 more elements\n", "\n", "  IfcBuildingStorey: Level 1 contains 25661 elements:\n", "    IfcColumn: 14460\n", "    IfcElectricMotor: 6192\n", "    IfcSolarDevice: 5009\n", "      - IfcSolarDevice: STRI_PV String:STRI_PV String 2x14:834692\n", "      - IfcSolarDevice: STRI_PV String:STRI_PV String 2x14:834693\n", "      - IfcSolarDevice: STRI_PV String:STRI_PV String 2x14:834694\n", "      - IfcSolarDevice: STRI_PV String:STRI_PV String 2x14:834695\n", "      - IfcSolarDevice: STRI_PV String:STRI_PV String 2x14:834696\n", "      ... and 25656 more elements\n"]}], "source": ["# Analyze spatial containment hierarchy\n", "print(\"\\n=== SPATIAL HIERARCHY ANALYSIS ===\")\n", "\n", "# Find spatial containment relationships\n", "spatial_contains = ifc_file.by_type('IfcRelContainedInSpatialStructure')\n", "aggregates = ifc_file.by_type('IfcRelAggregates')\n", "\n", "print(f\"Spatial Containment Relations: {len(spatial_contains)}\")\n", "print(f\"Aggregation Relations: {len(aggregates)}\")\n", "\n", "# Build spatial hierarchy\n", "spatial_hierarchy = defaultdict(list)\n", "element_locations = {}\n", "\n", "for rel in spatial_contains:\n", "    container = rel.RelatingStructure\n", "    contained_elements = rel.RelatedElements\n", "    \n", "    container_info = f\"{container.is_a()}: {getattr(container, 'Name', 'Unnamed')}\"\n", "    \n", "    for element in contained_elements:\n", "        element_info = f\"{element.is_a()}: {getattr(element, 'Name', 'Unnamed')}\"\n", "        spatial_hierarchy[container_info].append(element_info)\n", "        element_locations[element.GlobalId] = container_info\n", "\n", "print(f\"\\nSpatial Hierarchy:\")\n", "for container, elements in spatial_hierarchy.items():\n", "    print(f\"\\n  {container} contains {len(elements)} elements:\")\n", "    \n", "    # Group by element type\n", "    element_types = defaultdict(int)\n", "    for element in elements:\n", "        element_type = element.split(':')[0]\n", "        element_types[element_type] += 1\n", "    \n", "    for elem_type, count in sorted(element_types.items()):\n", "        print(f\"    {elem_type}: {count}\")\n", "    \n", "    # Show sample elements\n", "    if len(elements) <= 10:\n", "        for element in elements:\n", "            print(f\"      - {element}\")\n", "    else:\n", "        for element in elements[:5]:\n", "            print(f\"      - {element}\")\n", "        print(f\"      ... and {len(elements) - 5} more elements\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Data Quality Assessment and Extraction Strategy"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== DATA QUALITY ASSESSMENT ===\n", "Geometry Extraction Success Rates:\n", "  IfcColumn: 100.0% (20/20 tested, 14460 total)\n", "\n", "Coordinate Ranges by Element Type:\n", "\n", "=== EXTRACTION STRATEGY RECOMMENDATIONS ===\n", "High Success Rate Elements (>80%): ['IfcColumn']\n", "Medium Success Rate Elements (50-80%): []\n", "Low Success Rate Elements (<50%): []\n", "\n", "Recommended Extraction Approach:\n", "1. Focus on high-success elements for reliable coordinate extraction\n", "2. Use property sets for additional metadata where geometry fails\n", "3. Implement error handling for geometry extraction\n", "4. Consider coordinate system transformation based on CRS information\n", "5. Export multiple formats (coordinates only, full metadata, element-specific)\n", "\n", "Exploration summary saved: ../../data/processed/trino_enel/ifc_exploration/GRE.EEC.S.00.IT.P.14353.00.265_exploration_summary.json\n", "\n", "IFC STRUCTURE EXPLORATION COMPLETE\n", "Use insights from this analysis to optimize metadata extraction in notebook 01\n"]}], "source": ["# Assess data quality and determine extraction strategy\n", "print(\"\\n=== DATA QUALITY ASSESSMENT ===\")\n", "\n", "# Test geometry extraction success rates\n", "geometry_stats = {}\n", "coordinate_ranges = {}\n", "\n", "for element_type in ['IfcPile', 'IfcBeam', 'IfcColumn', 'IfcSlab', 'IfcWall']:\n", "    elements = ifc_file.by_type(element_type)\n", "    if not elements:\n", "        continue\n", "    \n", "    successful_extractions = 0\n", "    coordinates = []\n", "    \n", "    # Test first 20 elements for geometry extraction\n", "    test_elements = elements[:min(20, len(elements))]\n", "    \n", "    for element in test_elements:\n", "        try:\n", "            if hasattr(element, 'ObjectPlacement') and element.ObjectPlacement:\n", "                settings = ifcopenshell.geom.settings()\n", "                shape = ifcopenshell.geom.create_shape(settings, element)\n", "                if shape:\n", "                    successful_extractions += 1\n", "                    # Extract coordinates\n", "                    matrix = shape.transformation.matrix.data\n", "                    transformation_matrix = np.array(matrix).reshape(4, 4)\n", "                    position = transformation_matrix[:3, 3]\n", "                    coordinates.append(position)\n", "        except Exception:\n", "            continue\n", "    \n", "    success_rate = successful_extractions / len(test_elements) if test_elements else 0\n", "    geometry_stats[element_type] = {\n", "        'total_elements': len(elements),\n", "        'tested_elements': len(test_elements),\n", "        'successful_extractions': successful_extractions,\n", "        'success_rate': success_rate\n", "    }\n", "    \n", "    if coordinates:\n", "        coords_array = np.array(coordinates)\n", "        coordinate_ranges[element_type] = {\n", "            'x_range': [float(coords_array[:, 0].min()), float(coords_array[:, 0].max())],\n", "            'y_range': [float(coords_array[:, 1].min()), float(coords_array[:, 1].max())],\n", "            'z_range': [float(coords_array[:, 2].min()), float(coords_array[:, 2].max())]\n", "        }\n", "\n", "print(\"Geometry Extraction Success Rates:\")\n", "for element_type, stats in geometry_stats.items():\n", "    print(f\"  {element_type}: {stats['success_rate']:.1%} ({stats['successful_extractions']}/{stats['tested_elements']} tested, {stats['total_elements']} total)\")\n", "\n", "print(\"\\nCoordinate Ranges by Element Type:\")\n", "for element_type, ranges in coordinate_ranges.items():\n", "    print(f\"  {element_type}:\")\n", "    print(f\"    X: {ranges['x_range'][0]:.2f} to {ranges['x_range'][1]:.2f}\")\n", "    print(f\"    Y: {ranges['y_range'][0]:.2f} to {ranges['y_range'][1]:.2f}\")\n", "    print(f\"    Z: {ranges['z_range'][0]:.2f} to {ranges['z_range'][1]:.2f}\")\n", "\n", "# Generate extraction recommendations\n", "print(\"\\n=== EXTRACTION STRATEGY RECOMMENDATIONS ===\")\n", "\n", "high_success_elements = [elem for elem, stats in geometry_stats.items() if stats['success_rate'] > 0.8]\n", "medium_success_elements = [elem for elem, stats in geometry_stats.items() if 0.5 <= stats['success_rate'] <= 0.8]\n", "low_success_elements = [elem for elem, stats in geometry_stats.items() if stats['success_rate'] < 0.5]\n", "\n", "print(f\"High Success Rate Elements (>80%): {high_success_elements}\")\n", "print(f\"Medium Success Rate Elements (50-80%): {medium_success_elements}\")\n", "print(f\"Low Success Rate Elements (<50%): {low_success_elements}\")\n", "\n", "print(\"\\nRecommended Extraction Approach:\")\n", "print(\"1. Focus on high-success elements for reliable coordinate extraction\")\n", "print(\"2. Use property sets for additional metadata where geometry fails\")\n", "print(\"3. Implement error handling for geometry extraction\")\n", "print(\"4. Consider coordinate system transformation based on CRS information\")\n", "print(\"5. Export multiple formats (coordinates only, full metadata, element-specific)\")\n", "\n", "# Save exploration summary\n", "exploration_summary = {\n", "    'file_info': {\n", "        'schema': ifc_file.schema,\n", "        'total_entities': len(list(ifc_file))\n", "    },\n", "    'element_counts': dict(building_elements),\n", "    'geometry_extraction_stats': geometry_stats,\n", "    'coordinate_ranges': coordinate_ranges,\n", "    'property_sets': {name: {'count': info['count'], 'properties': list(info['properties'])} \n", "                     for name, info in pset_analysis.items()},\n", "    'recommendations': {\n", "        'high_success_elements': high_success_elements,\n", "        'medium_success_elements': medium_success_elements,\n", "        'low_success_elements': low_success_elements\n", "    }\n", "}\n", "\n", "summary_path = output_path / f\"{Path(ifc_file_path).stem}_exploration_summary.json\"\n", "with open(summary_path, 'w') as f:\n", "    json.dump(exploration_summary, f, indent=2)\n", "\n", "print(f\"\\nExploration summary saved: {summary_path}\")\n", "print(\"\\nIFC STRUCTURE EXPLORATION COMPLETE\")\n", "print(\"Use insights from this analysis to optimize metadata extraction in notebook 01\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Sample Data Structure Preview (pandas df.head() style)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SAMPLE METADATA STRUCTURE PREVIEW ===\n", "This demonstrates the expected pandas DataFrame structure for metadata extraction:\n", "\n", "Sample metadata structure (df.head() style):\n", "Shape: (3, 9)\n", "Columns: ['GlobalId', 'Name', 'Type', 'Description', 'Tag', 'X', 'Y', 'Z', 'GeometryExtracted']\n", "\n", "Sample data preview:\n", "                                      Name      Type    X    Y    Z  GeometryExtracted\n", "TRPL_Tracker Pile:TRPL_Tracker Pile:952577 IfcColumn None None None              False\n", "TRPL_Tracker Pile:TRPL_Tracker Pile:952578 IfcColumn None None None              False\n", "TRPL_Tracker Pile:TRPL_Tracker Pile:952579 IfcColumn None None None              False\n", "\n", "Data types:\n", "  GlobalId: object\n", "  Name: object\n", "  Type: object\n", "  Description: object\n", "  Tag: object\n", "  X: object\n", "  Y: object\n", "  Z: object\n", "  GeometryExtracted: bool\n", "\n", "Data completeness:\n", "  GlobalId: 3/3 (100.0%)\n", "  Name: 3/3 (100.0%)\n", "  Type: 3/3 (100.0%)\n", "  Description: 0/3 (0.0%)\n", "  Tag: 3/3 (100.0%)\n", "  X: 0/3 (0.0%)\n", "  Y: 0/3 (0.0%)\n", "  Z: 0/3 (0.0%)\n", "  GeometryExtracted: 3/3 (100.0%)\n", "\n", "This structure will be used in notebook 01 for systematic metadata extraction.\n"]}], "source": ["# Create sample DataFrame to demonstrate expected metadata structure\n", "print(\"\\n=== SAMPLE METADATA STRUCTURE PREVIEW ===\")\n", "print(\"This demonstrates the expected pandas DataFrame structure for metadata extraction:\")\n", "\n", "# Create sample data based on exploration findings\n", "sample_data = []\n", "\n", "# Use actual elements if available, otherwise create representative samples\n", "for element_type in ['IfcPile', 'IfcBeam', 'IfcColumn']:\n", "    elements = ifc_file.by_type(element_type)\n", "    if elements:\n", "        # Take first few elements for sample\n", "        for i, element in enumerate(elements[:3]):\n", "            try:\n", "                # Extract basic metadata\n", "                record = {\n", "                    'GlobalId': element.GlobalId,\n", "                    'Name': getattr(element, 'Name', f'{element_type}_{i+1}'),\n", "                    'Type': element.is_a(),\n", "                    'Description': getattr(element, 'Description', None),\n", "                    'Tag': getattr(element, 'Tag', None)\n", "                }\n", "                \n", "                # Try to extract coordinates\n", "                try:\n", "                    if hasattr(element, 'ObjectPlacement') and element.ObjectPlacement:\n", "                        settings = ifcopenshell.geom.settings()\n", "                        shape = ifcopenshell.geom.create_shape(settings, element)\n", "                        if shape:\n", "                            matrix = shape.transformation.matrix.data\n", "                            transformation_matrix = np.array(matrix).reshape(4, 4)\n", "                            position = transformation_matrix[:3, 3]\n", "                            \n", "                            record.update({\n", "                                'X': round(position[0], 3),\n", "                                'Y': round(position[1], 3),\n", "                                'Z': round(position[2], 3),\n", "                                'GeometryExtracted': True\n", "                            })\n", "                        else:\n", "                            record.update({\n", "                                'X': None,\n", "                                'Y': None,\n", "                                'Z': None,\n", "                                'GeometryExtracted': <PERSON><PERSON><PERSON>\n", "                            })\n", "                except Exception:\n", "                    record.update({\n", "                        'X': None,\n", "                        'Y': None,\n", "                        'Z': None,\n", "                        'GeometryExtracted': <PERSON><PERSON><PERSON>\n", "                    })\n", "                \n", "                sample_data.append(record)\n", "                \n", "            except Exception as e:\n", "                print(f\"Error processing sample {element_type}: {e}\")\n", "                continue\n", "\n", "# Create sample DataFrame\n", "if sample_data:\n", "    sample_df = pd.DataFrame(sample_data)\n", "    \n", "    print(f\"\\nSample metadata structure (df.head() style):\")\n", "    print(f\"Shape: {sample_df.shape}\")\n", "    print(f\"Columns: {list(sample_df.columns)}\")\n", "    \n", "    # Display sample data\n", "    print(f\"\\nSample data preview:\")\n", "    display_cols = ['Name', 'Type', 'X', 'Y', 'Z', 'GeometryExtracted']\n", "    available_cols = [col for col in display_cols if col in sample_df.columns]\n", "    \n", "    preview_df = sample_df[available_cols]\n", "    print(preview_df.to_string(index=False, float_format='%.3f'))\n", "    \n", "    # Show data types\n", "    print(f\"\\nData types:\")\n", "    for col in sample_df.columns:\n", "        print(f\"  {col}: {sample_df[col].dtype}\")\n", "    \n", "    # Show completeness\n", "    print(f\"\\nData completeness:\")\n", "    for col in sample_df.columns:\n", "        non_null_count = sample_df[col].notna().sum()\n", "        total_count = len(sample_df)\n", "        completeness = non_null_count / total_count * 100\n", "        print(f\"  {col}: {non_null_count}/{total_count} ({completeness:.1f}%)\")\n", "    \n", "    print(f\"\\nThis structure will be used in notebook 01 for systematic metadata extraction.\")\n", "    \n", "else:\n", "    print(\"No sample data could be extracted for preview\")\n", "    print(\"Expected DataFrame structure:\")\n", "    print(\"Columns: ['GlobalId', 'Name', 'Type', 'Description', 'Tag', 'X', 'Y', 'Z', 'GeometryExtracted']\")\n", "    print(\"This structure will guide the implementation in notebook 01\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}
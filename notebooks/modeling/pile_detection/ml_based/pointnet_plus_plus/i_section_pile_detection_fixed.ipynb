{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# I-Section Pile Detection (PointNet++) - FIXED VERSION\n", "\n", "This notebook implements a working I-section pile detection using geometric rules as a foundation, with PointNet++ architecture ready for future training.\n", "\n", "**Key Fix**: Replace mock detections with actual geometric-based pile detection\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "site_name = \"trino_enel\"\n", "ground_method = \"csf\"  # Options: csf, pmf, ransac\n", "confidence_threshold = 0.7\n", "output_dir = \"../../data/output_runs/pile_detection\"\n", "enable_visualization = True\n", "save_results = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import pandas as pd\n", "from sklearn.cluster import DBSCAN\n", "from sklearn.neighbors import NearestNeighbors\n", "import json\n", "from datetime import datetime\n", "\n", "# Setup\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"🔧 I-SECTION PILE DETECTION - {ground_method.upper()}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Point Cloud Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load non-ground point cloud from ground segmentation\n", "nonground_file = Path(f\"../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply\")\n", "\n", "if not nonground_file.exists():\n", "    print(f\"❌ File not found: {nonground_file}\")\n", "    # Try alternative naming\n", "    alt_file = Path(f\"../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply\")\n", "    if alt_file.exists():\n", "        nonground_file = alt_file\n", "        print(f\"✅ Using alternative file: {nonground_file}\")\n", "    else:\n", "        raise FileNotFoundError(f\"No non-ground point cloud found for {site_name} with {ground_method}\")\n", "\n", "# Load point cloud\n", "pcd = o3d.io.read_point_cloud(str(nonground_file))\n", "points = np.asarray(pcd.points)\n", "\n", "print(f\"✅ Loaded point cloud: {points.shape[0]:,} points\")\n", "print(f\"Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}] Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}] Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Geometric-Based I-Section Pile Detection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class GeometricISectionDetector:\n", "    \"\"\"Geometric-based I-section pile detection using point cloud analysis.\"\"\"\n", "    \n", "    def __init__(self):\n", "        # I-section pile characteristics\n", "        self.min_height = 1.5  # Minimum pile height above ground\n", "        self.max_height = 10.0  # Maximum reasonable pile height\n", "        self.typical_width = 0.2  # Typical I-section width (~20cm)\n", "        self.width_tolerance = 0.1  # ±10cm tolerance\n", "        self.min_points_per_pile = 50  # Minimum points to consider as pile\n", "        \n", "    def detect_vertical_structures(self, points):\n", "        \"\"\"Detect vertical structures that could be piles.\"\"\"\n", "        print(\"🔍 Detecting vertical structures...\")\n", "        \n", "        # Filter by height (structures extending above ground)\n", "        z_min = points[:, 2].min()\n", "        height_mask = (points[:, 2] - z_min) >= self.min_height\n", "        elevated_points = points[height_mask]\n", "        \n", "        print(f\"Points above {self.min_height}m: {len(elevated_points):,}\")\n", "        \n", "        if len(elevated_points) < self.min_points_per_pile:\n", "            return []\n", "        \n", "        # Cluster elevated points to find individual structures\n", "        clustering = DBSCAN(eps=0.5, min_samples=self.min_points_per_pile)\n", "        cluster_labels = clustering.fit_predict(elevated_points[:, :2])  # Cluster in XY plane\n", "        \n", "        # Extract clusters\n", "        unique_labels = set(cluster_labels)\n", "        unique_labels.discard(-1)  # Remove noise label\n", "        \n", "        vertical_structures = []\n", "        for label in unique_labels:\n", "            cluster_mask = cluster_labels == label\n", "            cluster_points = elevated_points[cluster_mask]\n", "            \n", "            if len(cluster_points) >= self.min_points_per_pile:\n", "                vertical_structures.append(cluster_points)\n", "        \n", "        print(f\"Found {len(vertical_structures)} vertical structure candidates\")\n", "        return vertical_structures\n", "    \n", "    def analyze_i_section_characteristics(self, structure_points):\n", "        \"\"\"Analyze if structure has I-section characteristics.\"\"\"\n", "        # Calculate structure dimensions\n", "        x_span = structure_points[:, 0].max() - structure_points[:, 0].min()\n", "        y_span = structure_points[:, 1].max() - structure_points[:, 1].min()\n", "        z_span = structure_points[:, 2].max() - structure_points[:, 2].min()\n", "        \n", "        # I-section characteristics:\n", "        # 1. Significant height (vertical structure)\n", "        # 2. Moderate width (flange width)\n", "        # 3. Elongated in one horizontal direction (beam orientation)\n", "        \n", "        height_score = min(z_span / self.min_height, 1.0)  # Normalize to [0,1]\n", "        \n", "        # Check if width is reasonable for I-section\n", "        max_horizontal = max(x_span, y_span)\n", "        min_horizontal = min(x_span, y_span)\n", "        \n", "        width_score = 0.0\n", "        if (self.typical_width - self.width_tolerance) <= max_horizontal <= (self.typical_width + self.width_tolerance):\n", "            width_score = 0.8\n", "        \n", "        # I-sections are often elongated (beam length > flange width)\n", "        aspect_ratio = max_horizontal / (min_horizontal + 1e-6)\n", "        elongation_score = min(aspect_ratio / 3.0, 1.0)  # Prefer aspect ratio > 3:1\n", "        \n", "        # Combine scores\n", "        confidence = (height_score * 0.4 + width_score * 0.4 + elongation_score * 0.2)\n", "        \n", "        return {\n", "            'confidence': confidence,\n", "            'height': z_span,\n", "            'width': max_horizontal,\n", "            'thickness': min_horizontal,\n", "            'aspect_ratio': aspect_ratio,\n", "            'point_count': len(structure_points)\n", "        }\n", "    \n", "    def extract_pile_center(self, structure_points):\n", "        \"\"\"Extract pile center coordinates.\"\"\"\n", "        # Use centroid of bottom 20% of points (pile base)\n", "        z_min = structure_points[:, 2].min()\n", "        z_threshold = z_min + 0.2 * (structure_points[:, 2].max() - z_min)\n", "        \n", "        base_mask = structure_points[:, 2] <= z_threshold\n", "        base_points = structure_points[base_mask]\n", "        \n", "        if len(base_points) > 0:\n", "            center = base_points.mean(axis=0)\n", "        else:\n", "            center = structure_points.mean(axis=0)\n", "        \n", "        return center\n", "    \n", "    def detect_i_section_piles(self, points):\n", "        \"\"\"Main detection pipeline.\"\"\"\n", "        print(\"🔧 Starting I-section pile detection...\")\n", "        \n", "        # Step 1: Find vertical structures\n", "        vertical_structures = self.detect_vertical_structures(points)\n", "        \n", "        if not vertical_structures:\n", "            print(\"No vertical structures found\")\n", "            return []\n", "        \n", "        # Step 2: Analyze each structure for I-section characteristics\n", "        detections = []\n", "        for i, structure in enumerate(vertical_structures):\n", "            analysis = self.analyze_i_section_characteristics(structure)\n", "            \n", "            if analysis['confidence'] >= confidence_threshold:\n", "                center = self.extract_pile_center(structure)\n", "                \n", "                detection = {\n", "                    'x': float(center[0]),\n", "                    'y': float(center[1]),\n", "                    'z': float(center[2]),\n", "                    'confidence': float(analysis['confidence']),\n", "                    'width': float(analysis['width']),\n", "                    'height': float(analysis['height']),\n", "                    'thickness': float(analysis['thickness']),\n", "                    'i_section_score': float(analysis['confidence']),\n", "                    'point_count': int(analysis['point_count']),\n", "                    'detection_method': 'geometric'\n", "                }\n", "                \n", "                detections.append(detection)\n", "        \n", "        print(f\"✅ Detected {len(detections)} I-section pile candidates\")\n", "        return detections\n", "\n", "# Initialize detector\n", "detector = GeometricISectionDetector()\n", "print(\"✅ Geometric I-section detector initialized\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run Detection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run pile detection\n", "final_detections = detector.detect_i_section_piles(points)\n", "\n", "print(f\"\\n📊 I-SECTION PILE DETECTION RESULTS\")\n", "print(f\"Total detections: {len(final_detections)}\")\n", "print(f\"Confidence threshold: {confidence_threshold}\")\n", "\n", "if final_detections:\n", "    confidences = [d['confidence'] for d in final_detections]\n", "    heights = [d['height'] for d in final_detections]\n", "    widths = [d['width'] for d in final_detections]\n", "    \n", "    print(f\"\\nConfidence statistics:\")\n", "    print(f\"  Mean: {np.mean(confidences):.3f}\")\n", "    print(f\"  Range: [{np.min(confidences):.3f}, {np.max(confidences):.3f}]\")\n", "    \n", "    print(f\"\\nDimension statistics:\")\n", "    print(f\"  Height range: [{np.min(heights):.2f}, {np.max(heights):.2f}] m\")\n", "    print(f\"  Width range: [{np.min(widths):.2f}, {np.max(widths):.2f}] m\")\n", "    \n", "    # Display individual detections\n", "    print(f\"\\nDetailed results:\")\n", "    for i, det in enumerate(final_detections):\n", "        print(f\"  Pile {i+1}: ({det['x']:.1f}, {det['y']:.1f}, {det['z']:.1f}) \"\n", "              f\"conf={det['confidence']:.3f} h={det['height']:.2f}m w={det['width']:.2f}m\")\n", "else:\n", "    print(\"No piles detected above confidence threshold\")\n", "    print(\"Consider lowering confidence_threshold or checking point cloud quality\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if enable_visualization and final_detections:\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Plot 1: Top view with detections\n", "    ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], s=1, alpha=0.6, cmap='viridis')\n", "    \n", "    # Overlay detections\n", "    for det in final_detections:\n", "        circle = plt.Circle((det['x'], det['y']), det['width']/2, \n", "                          fill=False, color='red', linewidth=2)\n", "        ax1.add_patch(circle)\n", "        ax1.text(det['x'], det['y'], f\"{det['confidence']:.2f}\", \n", "                ha='center', va='center', color='red', fontweight='bold')\n", "    \n", "    ax1.set_xlabel('X (m)')\n", "    ax1.set_ylabel('Y (m)')\n", "    ax1.set_title(f'I-Section Pile Detection - Top View\\n{len(final_detections)} detections')\n", "    ax1.axis('equal')\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Plot 2: Side view\n", "    ax2.scatter(points[:, 0], points[:, 2], c='blue', s=1, alpha=0.6)\n", "    \n", "    # Overlay detections\n", "    for det in final_detections:\n", "        ax2.scatter(det['x'], det['z'], c='red', s=100, marker='x', linewidth=3)\n", "        ax2.text(det['x'], det['z'] + 0.5, f\"{det['confidence']:.2f}\", \n", "                ha='center', va='bottom', color='red', fontweight='bold')\n", "    \n", "    ax2.set_xlabel('X (m)')\n", "    ax2.set_ylabel('Z (m)')\n", "    ax2.set_title('I-Section Pile Detection - Side View')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    if save_results:\n", "        plt.savefig(output_path / f'i_section_detection_{ground_method}.png', dpi=300, bbox_inches='tight')\n", "        print(f\"📊 Saved visualization: {output_path / f'i_section_detection_{ground_method}.png'}\")\n", "    \n", "    plt.show()\n", "else:\n", "    print(\"No detections to visualize\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if save_results:\n", "    print(\"\\n💾 SAVING RESULTS\")\n", "    \n", "    # Save detections CSV\n", "    if final_detections:\n", "        df = pd.DataFrame(final_detections)\n", "        csv_file = output_path / f'i_section_detections_{ground_method}.csv'\n", "        df.to_csv(csv_file, index=False)\n", "        print(f\"✅ Saved detections: {csv_file}\")\n", "    \n", "    # Save metrics\n", "    metrics = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'site_name': site_name,\n", "        'ground_method': ground_method,\n", "        'detection_method': 'geometric',\n", "        'confidence_threshold': confidence_threshold,\n", "        'total_detections': len(final_detections),\n", "        'input_points': int(len(points)),\n", "        'detection_statistics': {\n", "            'mean_confidence': float(np.mean([d['confidence'] for d in final_detections])) if final_detections else 0.0,\n", "            'mean_height': float(np.mean([d['height'] for d in final_detections])) if final_detections else 0.0,\n", "            'mean_width': float(np.mean([d['width'] for d in final_detections])) if final_detections else 0.0\n", "        }\n", "    }\n", "    \n", "    metrics_file = output_path / f'i_section_metrics_{ground_method}.json'\n", "    with open(metrics_file, 'w') as f:\n", "        json.dump(metrics, f, indent=2)\n", "    print(f\"✅ Saved metrics: {metrics_file}\")\n", "    \n", "    print(f\"\\n🎯 All results saved to: {output_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"🎯 I-SECTION PILE DETECTION SUMMARY\")\n", "print(\"=\"*60)\n", "print(f\"Site: {site_name}\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Detection method: Geometric-based\")\n", "print(f\"Input points: {len(points):,}\")\n", "print(f\"Detections found: {len(final_detections)}\")\n", "print(f\"Success rate: {'✅ GOOD' if len(final_detections) > 0 else '⚠️ NO DETECTIONS'}\")\n", "print(\"=\"*60)\n", "print(\"\\n🔄 Ready for integration with alignment pipeline!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}
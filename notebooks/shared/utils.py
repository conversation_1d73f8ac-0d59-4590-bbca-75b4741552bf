from pathlib import Path
import logging

logger = logging.getLogger(__name__)

def resolve_point_cloud_path(path_input):
    """
    Resolve and validate the point cloud file path.
    Automatically switches between `.las` and `.ply` if needed.
    """
    if not path_input:
        raise ValueError("point_cloud_path must be provided as a Papermill parameter.")

    point_cloud_file = Path(path_input)

    if not point_cloud_file.exists():
        alternative_ext = ".ply" if point_cloud_file.suffix == ".las" else ".las"
        alt_file = point_cloud_file.with_suffix(alternative_ext)

        if alt_file.exists():
            logger.warning(
                f"{point_cloud_file.suffix} file not found. Using alternative: {alt_file}"
            )
            point_cloud_file = alt_file
        else:
            raise FileNotFoundError(
                f"Neither {point_cloud_file} nor {alt_file} found."
            )

    if not point_cloud_file.is_file():
        raise ValueError(f"Provided path is not a file: {point_cloud_file}")

    logger.info(f"Using point cloud file: {point_cloud_file}")
    return point_cloud_file

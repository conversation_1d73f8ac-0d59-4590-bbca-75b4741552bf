from pathlib import Path
import os
from datetime import datetime

# Project root detection
PROJECT_ROOT = Path(__file__).parent.parent.parent
DATA_ROOT = PROJECT_ROOT / "data"
OUTPUT_ROOT = DATA_ROOT / "output_runs"

# Standard path functions
def get_data_path(site_name, data_type="raw"):
    """Get standardized data path for any site and data type"""
    return DATA_ROOT / data_type / site_name

def get_output_path(notebook_type, site_name, timestamp=None):
    """Get standardized output path with timestamp"""
    if timestamp is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return OUTPUT_ROOT / notebook_type / f"{site_name}_{timestamp}"

def get_mlflow_tracking_uri():
    """Get MLflow tracking URI"""
    return str(DATA_ROOT / "mlruns")

def get_processed_data_path(site_name, processing_stage):
    """Get path to processed data for specific stage"""
    return DATA_ROOT / "processed" / site_name / processing_stage

def find_latest_file(search_path, pattern):
    """Find the most recent file matching pattern"""
    matching_files = list(search_path.glob(pattern))
    if not matching_files:
        raise FileNotFoundError(
            f"No files matching '{pattern}' found in {search_path}.\n"
            f"Available files: {[f.name for f in search_path.iterdir()]}"
        )
    return max(matching_files, key=lambda f: f.stat().st_mtime)

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# TensorFlow
logs/
saved_models/
checkpoints/
# Ignore all mlruns folders
**/mlruns/
notebooks/preprocessing/ground_segmentation/mlruns/
output_runs/
notebooks/data

data/
backup-not-to-delete/
testing_schedule/
docs/
*.md
*.pdf
*.png
*.ply
*.pcd
*.xyz
*.json
*.las
*.laz
*.csv


# Project specific
results/
data/*.obj
data/*.las
data/*.ply
data/*.pcd

# Generated PDFs
docs/project_proposal/*.pdf

# Jupyter Notebooks
.ipynb_checkpoints
notebooks/.ipynb_checkpoints/

# OS specific
.DS_Store
.env
.venv
venv/
ENV/
pdf_env/

# IDE
.idea/
.vscode/
*.swp
*.swo
*.md
/notebooks/preprocessing/ground_segmentation/mlruns
/notebooks/preprocessing/ground_segmentation/mlruns
